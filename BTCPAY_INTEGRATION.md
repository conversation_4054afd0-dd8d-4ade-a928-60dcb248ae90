# BTCPay Server Integration - Payment & Payout Flow

## 🎯 Overview
RSGlider uses BTCPay Server for all Bitcoin transactions - both customer payments and developer payouts. This eliminates the need for storing funds or managing wallets, reducing security risks and regulatory complexity.

## 💳 Customer Payment Flow

### 1. Shopping & Checkout
```
User adds items to cart → POST /cart/checkout → BTCPay invoice created
```

**Process:**
1. User browses marketplace and adds items to cart
2. User initiates checkout with `POST /cart/checkout`
3. RSGlider creates BTCPay Server invoice
4. User gets payment URL, QR code, and Bitcoin address
5. User pays Bitcoin directly to BTCPay Server
6. BTCPay Server webhook notifies RSGlider of payment
7. RSGlider grants access to purchased items

### 2. Payment Tracking
```
GET /orders/{orderId}/status → Real-time payment status
```

**Statuses:**
- `pending` - Waiting for payment
- `paid` - Payment received, processing
- `completed` - Access granted
- `expired` - Payment window expired
- `cancelled` - Payment cancelled

### 3. No Stored Funds
- **Direct payment**: Bitcoin goes directly to BTCPay Server
- **No wallets**: RSGlider never holds customer funds
- **Instant conversion**: BTCPay can auto-convert to fiat if needed
- **Security**: Eliminates wallet security risks

## 💰 Developer Payout Flow

### 1. Earnings Accumulation
```
Customer purchase → Revenue split → Developer earnings tracked
```

**Process:**
1. Customer buys developer's item
2. Revenue split applied (default 70% developer, 30% platform)
3. Developer earnings tracked in USD
4. Holding period applied (default 7 days)

### 2. Payout Methods

#### Automatic Payouts
```
Weekly schedule → Check thresholds → Create BTCPay payout → Send Bitcoin
```

**Configuration:**
- **Default frequency**: Weekly (configurable per user/role)
- **Minimum threshold**: $50 USD equivalent
- **Holding period**: 7 days from sale
- **Auto-conversion**: USD earnings → Bitcoin at current rate

#### Manual Payout Requests
```
POST /developers/payouts/request → Admin approval → BTCPay payout
```

**Developer Actions:**
- `GET /developers/payouts/available` - Check available earnings
- `POST /developers/payouts/request` - Request manual payout
- `PUT /developers/payouts/settings` - Update Bitcoin address

### 3. Flexible Configuration

#### Global Settings (Admin)
```yaml
payout:
  default_minimum: 50      # USD
  default_frequency: weekly
  default_holding_period: 7 # days
  auto_payout_enabled: true
```

#### Role-Based Settings
```yaml
premium_developer:
  minimum: 25              # Lower threshold
  frequency: daily         # More frequent
  holding_period: 3        # Shorter hold
```

#### User-Specific Overrides
```yaml
user_12345:
  minimum: 100             # Higher threshold
  frequency: monthly       # Less frequent
  auto_payout: false       # Manual only
```

## 🔧 BTCPay Server Configuration

### Store Setup
```yaml
btcpay:
  server_url: "https://btcpay.rsglider.com"
  store_id: "rsglider-main"
  api_key: "${BTCPAY_API_KEY}"
  webhook_secret: "${BTCPAY_WEBHOOK_SECRET}"
```

### Payment Settings
- **Network**: Bitcoin mainnet
- **Confirmation target**: 2 blocks
- **Invoice expiry**: 15 minutes
- **Payment tolerance**: 0% (exact amount required)

### Payout Settings
- **Network fee policy**: Platform pays (configurable)
- **Batch payouts**: Enabled for efficiency
- **Rate source**: CoinGecko API
- **Conversion timing**: At payout creation

## 🔄 Webhook Integration

### Customer Payment Webhooks
```
BTCPay Server → POST /webhooks/btcpay/payment → Update order status
```

**Events:**
- `InvoiceCreated` - Payment initiated
- `InvoiceReceivedPayment` - Payment detected
- `InvoiceProcessing` - Confirming payment
- `InvoiceSettled` - Payment confirmed
- `InvoiceExpired` - Payment expired

### Payout Webhooks
```
BTCPay Server → POST /webhooks/btcpay/payout → Update payout status
```

**Events:**
- `PayoutCreated` - Payout initiated
- `PayoutProcessing` - Broadcasting transaction
- `PayoutCompleted` - Transaction confirmed
- `PayoutFailed` - Transaction failed

## 🛡️ Security Features

### Payment Security
- **No stored funds**: Payments go directly to BTCPay
- **Webhook signatures**: Verify all webhook calls
- **Invoice validation**: Validate amounts and metadata
- **Rate limiting**: Prevent payment spam

### Payout Security
- **Address verification**: Require verified Bitcoin addresses
- **Admin approval**: Optional manual approval for large payouts
- **Audit trails**: Log all payout requests and approvals
- **Fraud detection**: Monitor unusual payout patterns

## 📊 Analytics & Reporting

### Revenue Tracking
- Real-time revenue analytics
- Developer earnings breakdown
- Platform fee calculations
- Bitcoin conversion rates

### Payout Monitoring
- Pending payout amounts
- Payout success rates
- Network fee costs
- Developer satisfaction metrics

## 🚀 Implementation Benefits

### For RSGlider
- **No wallet management**: BTCPay handles all Bitcoin operations
- **Reduced liability**: No stored customer funds
- **Regulatory compliance**: Simplified compliance requirements
- **Scalability**: BTCPay handles payment processing load

### For Customers
- **Direct payments**: Bitcoin goes directly to secure infrastructure
- **Transparent pricing**: Real-time Bitcoin conversion rates
- **Multiple wallets**: Pay from any Bitcoin wallet
- **Privacy**: No account funding required

### For Developers
- **Flexible payouts**: Configurable schedules and thresholds
- **Bitcoin earnings**: Direct Bitcoin payments
- **Real-time tracking**: Live earnings and payout status
- **Low fees**: Efficient batch payouts reduce network costs

This architecture provides a secure, scalable, and compliant payment system that leverages Bitcoin's benefits while minimizing operational complexity and security risks.
