#!/bin/bash

# RSGlider API Local Development Setup
# Simple script to get PostgreSQL and Redis running locally

set -e

echo "🚀 Setting up RSGlider API local development environment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.development..."
    cp .env.development .env
    echo "✅ .env file created."
else
    echo "✅ .env file already exists."
fi

# Pull latest images
echo "📦 Pulling latest Docker images..."
docker-compose pull

# Start the full stack
echo "🚀 Starting full RSGlider stack..."
docker-compose up -d

# Wait a bit for services to start
echo "⏳ Waiting for services to start..."
sleep 10

# Check if services are running
echo "🔍 Checking service status..."
docker-compose ps

echo ""
echo "🎉 Local development environment is ready!"
echo ""
echo "📊 Services:"
echo "  • RSGlider API:     http://localhost:3000"
echo "  • API Docs:         http://localhost:3000/api/docs"
echo "  • PostgreSQL:       localhost:5432"
echo "  • Redis:            localhost:6379"
echo "  • Redis Commander:  http://localhost:8091"
echo ""
echo "🔧 Useful commands:"
echo "  • Stop all:         npm run docker:down"
echo "  • View all logs:    npm run docker:logs"
echo "  • View API logs:    npm run docker:logs:api"
echo "  • Restart API:      npm run docker:restart:api"
echo "  • Database shell:   npm run db:shell"
echo "  • Redis shell:      npm run redis:shell"
echo ""
echo "📝 Full stack is running!"
echo "  • The NestJS API will auto-reload on code changes"
echo "  • Database is initialized with schema and admin user"
echo "  • All services are connected and ready for development"
echo ""
echo "🎯 Ready to develop!"
