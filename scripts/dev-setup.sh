#!/bin/bash

# RSGlider API Local Development Setup
# Simple script to get PostgreSQL and Redis running locally

set -e

echo "🚀 Setting up RSGlider API local development environment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.development..."
    cp .env.development .env
    echo "✅ .env file created."
else
    echo "✅ .env file already exists."
fi

# Pull latest images
echo "📦 Pulling latest Docker images..."
docker-compose pull

# Start the services
echo "🚀 Starting PostgreSQL and Redis..."
docker-compose up -d postgres redis

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
timeout 60 bash -c 'until docker-compose exec postgres pg_isready -U rsglider -d rsglider; do sleep 2; done'

# Wait for Redis to be ready
echo "⏳ Waiting for Redis to be ready..."
timeout 30 bash -c 'until docker-compose exec redis redis-cli -a rsglider_redis_password ping; do sleep 2; done'

# Start development tools
echo "🛠️ Starting development tools..."
docker-compose up -d adminer redis-commander

echo ""
echo "🎉 Local development environment is ready!"
echo ""
echo "📊 Services:"
echo "  • PostgreSQL:       localhost:5432"
echo "  • Redis:            localhost:6379"
echo "  • Database Admin:   http://localhost:8080"
echo "  • Redis Commander:  http://localhost:8081"
echo ""
echo "🔧 Useful commands:"
echo "  • Stop services:    docker-compose down"
echo "  • Database shell:   docker-compose exec postgres psql -U rsglider -d rsglider"
echo "  • Redis shell:      docker-compose exec redis redis-cli -a rsglider_redis_password"
echo ""
echo "📝 Next steps:"
echo "  1. Install Node.js dependencies: npm install"
echo "  2. Start your NestJS app: npm run start:dev"
echo "  3. Visit API docs: http://localhost:3000/api/docs"
echo ""
echo "🎯 Ready to develop!"
