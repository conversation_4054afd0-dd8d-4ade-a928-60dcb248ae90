#!/bin/bash

# RSGlider API Development Setup Script
# This script sets up the development environment

set -e

echo "🚀 Setting up RSGlider API development environment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.development..."
    cp .env.development .env
    echo "✅ .env file created. Please review and update the values as needed."
else
    echo "✅ .env file already exists."
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p uploads
mkdir -p logs
mkdir -p docker/postgres/data
mkdir -p docker/redis/data

# Set proper permissions
chmod 755 uploads logs

# Pull latest images
echo "📦 Pulling latest Docker images..."
docker-compose pull

# Build the application
echo "🔨 Building the application..."
docker-compose build

# Start the services
echo "🚀 Starting development services..."
docker-compose up -d postgres redis

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
timeout 60 bash -c 'until docker-compose exec postgres pg_isready -U rsglider -d rsglider; do sleep 2; done'

# Wait for Redis to be ready
echo "⏳ Waiting for Redis to be ready..."
timeout 30 bash -c 'until docker-compose exec redis redis-cli ping; do sleep 2; done'

# Start the API
echo "🚀 Starting API service..."
docker-compose up -d api

# Start development tools
echo "🛠️ Starting development tools..."
docker-compose up -d adminer redis-commander mailhog

# Show status
echo ""
echo "🎉 Development environment is ready!"
echo ""
echo "📊 Services:"
echo "  • API:              http://localhost:3000"
echo "  • API Docs:         http://localhost:3000/api/docs"
echo "  • Database Admin:   http://localhost:8080"
echo "  • Redis Commander:  http://localhost:8081"
echo "  • Mail Testing:     http://localhost:8025"
echo ""
echo "🔧 Useful commands:"
echo "  • View logs:        docker-compose logs -f api"
echo "  • Stop services:    docker-compose down"
echo "  • Restart API:      docker-compose restart api"
echo "  • Database shell:   docker-compose exec postgres psql -U rsglider -d rsglider"
echo "  • Redis shell:      docker-compose exec redis redis-cli"
echo ""
echo "📝 Next steps:"
echo "  1. Review and update .env file with your configuration"
echo "  2. Install dependencies: npm install"
echo "  3. Run database migrations (when implemented)"
echo "  4. Start developing!"
echo ""

# Check if services are healthy
echo "🏥 Checking service health..."
sleep 10

if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ API is healthy"
else
    echo "⚠️  API health check failed. Check logs: docker-compose logs api"
fi

if docker-compose exec postgres pg_isready -U rsglider -d rsglider > /dev/null 2>&1; then
    echo "✅ Database is healthy"
else
    echo "⚠️  Database health check failed. Check logs: docker-compose logs postgres"
fi

if docker-compose exec redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis is healthy"
else
    echo "⚠️  Redis health check failed. Check logs: docker-compose logs redis"
fi

echo ""
echo "🎯 Development environment setup complete!"
