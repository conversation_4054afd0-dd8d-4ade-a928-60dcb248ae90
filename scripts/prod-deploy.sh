#!/bin/bash

# RSGlider API Production Deployment Script
# This script deploys the application to production

set -e

echo "🚀 Deploying RSGlider API to production..."

# Check if .env.production exists
if [ ! -f .env.production ]; then
    echo "❌ .env.production file not found. Please create it with production configuration."
    exit 1
fi

# Backup current deployment
echo "💾 Creating backup of current deployment..."
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Backup database
echo "📦 Backing up database..."
docker-compose exec postgres pg_dump -U rsglider rsglider > "$BACKUP_DIR/database.sql"

# Backup volumes
echo "📦 Backing up volumes..."
docker run --rm -v rsglider_postgres_data:/data -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/postgres_data.tar.gz -C /data .
docker run --rm -v rsglider_redis_data:/data -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/redis_data.tar.gz -C /data .
docker run --rm -v rsglider_api_uploads:/data -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/api_uploads.tar.gz -C /data .

echo "✅ Backup created in $BACKUP_DIR"

# Pull latest code (if using git)
if [ -d .git ]; then
    echo "📥 Pulling latest code..."
    git pull origin main
fi

# Build production images
echo "🔨 Building production images..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml build --no-cache

# Stop current services gracefully
echo "🛑 Stopping current services..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml down --timeout 30

# Start database and Redis first
echo "🗄️ Starting database and Redis..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d postgres redis

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
timeout 60 bash -c 'until docker-compose exec postgres pg_isready -U rsglider -d rsglider; do sleep 2; done'
timeout 30 bash -c 'until docker-compose exec redis redis-cli ping; do sleep 2; done'

# Run database migrations (when implemented)
echo "🔄 Running database migrations..."
# docker-compose -f docker-compose.yml -f docker-compose.prod.yml exec api npm run migration:run

# Start all services
echo "🚀 Starting all services..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Wait for API to be ready
echo "⏳ Waiting for API to be ready..."
timeout 120 bash -c 'until curl -f http://localhost:3000/health; do sleep 5; done'

# Clean up old images
echo "🧹 Cleaning up old images..."
docker image prune -f

# Show deployment status
echo ""
echo "🎉 Production deployment complete!"
echo ""
echo "📊 Services:"
echo "  • API:              http://localhost:3000"
echo "  • API Docs:         http://localhost:3000/api/docs"
echo "  • Nginx:            http://localhost:80"
echo ""
echo "🔧 Useful commands:"
echo "  • View logs:        docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs -f"
echo "  • Check status:     docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps"
echo "  • Scale API:        docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --scale api=3"
echo ""

# Health checks
echo "🏥 Running health checks..."
sleep 15

if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ API is healthy"
else
    echo "❌ API health check failed"
    exit 1
fi

if curl -f http://localhost:80/health > /dev/null 2>&1; then
    echo "✅ Nginx is healthy"
else
    echo "⚠️  Nginx health check failed (may be expected if not configured)"
fi

echo ""
echo "🎯 Production deployment successful!"
echo "📝 Backup location: $BACKUP_DIR"
