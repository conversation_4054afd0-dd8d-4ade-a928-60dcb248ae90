#!/usr/bin/env ts-node

import * as fs from 'fs';
import * as yaml from 'js-yaml';
import { OpenAPIV3 } from 'openapi-types';
import * as path from 'path';

class SimpleAPIGenerator {
  private spec: OpenAPIV3.Document;
  private srcDir: string;

  constructor() {
    this.srcDir = path.join(process.cwd(), 'src');
  }

  async generate(): Promise<void> {
    console.log('🚀 Generating clean NestJS API with aliased imports...');

    // Load OpenAPI spec
    const openApiPath = path.join(process.cwd(), 'api-docs', 'openapi.yaml');
    const openApiContent = fs.readFileSync(openApiPath, 'utf8');
    this.spec = yaml.load(openApiContent) as OpenAPIV3.Document;

    // Clean up old broken files
    this.cleanupBrokenFiles();

    // Generate DTOs with proper names
    this.generateCleanDTOs();

    // Generate basic controllers
    this.generateBasicControllers();

    // Fix main.ts to use aliased imports
    this.fixMainTsImports();

    console.log('✅ Clean API generated successfully!');
  }

  private cleanupBrokenFiles(): void {
    console.log('🧹 Cleaning up broken generated files...');

    // Remove all broken controllers/services/modules
    const moduleNames = ['auth', 'users', 'admin', 'store', 'developer', 'webhooks'];

    moduleNames.forEach(moduleName => {
      const modulePath = path.join(this.srcDir, moduleName);
      if (fs.existsSync(modulePath)) {
        // Remove broken generated files
        const filesToRemove = [
          `${moduleName}.controller.ts`,
          `${moduleName}.service.ts`,
          `${moduleName}.module.ts`
        ];

        filesToRemove.forEach(file => {
          const filePath = path.join(modulePath, file);
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            console.log(`  ✓ Removed broken ${file}`);
          }
        });
      }
    });
  }

  private generateCleanDTOs(): void {
    console.log('📝 Generating clean DTOs with proper names...');

    if (!this.spec.components?.schemas) return;

    Object.entries(this.spec.components.schemas).forEach(([schemaName, schema]) => {
      const schemaObj = schema as OpenAPIV3.SchemaObject;
      const moduleName = this.getModuleFromSchemaName(schemaName);
      const modulePath = path.join(this.srcDir, moduleName);

      // Ensure module directory exists
      this.ensureDirectoryExists(modulePath);
      this.ensureDirectoryExists(path.join(modulePath, 'dto'));

      // Generate DTO with PROPER name (no leading dash!)
      const dtoContent = this.generateDTO(schemaName, schemaObj);
      const fileName = this.toProperKebabCase(schemaName) + '.dto.ts';
      const dtoPath = path.join(modulePath, 'dto', fileName);

      fs.writeFileSync(dtoPath, dtoContent);
      console.log(`  ✓ Generated ${moduleName}/${fileName}`);
    });
  }

  private generateDTO(schemaName: string, schema: OpenAPIV3.SchemaObject): string {
    let content = `import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class ${schemaName} {
`;

    if (schema.properties) {
      Object.entries(schema.properties).forEach(([propName, propSchema]) => {
        const property = propSchema as OpenAPIV3.SchemaObject;
        const isRequired = schema.required?.includes(propName) || false;

        // Add decorators
        content += `  @ApiProperty(${!isRequired ? '{ required: false }' : ''})\n`;

        if (!isRequired) {
          content += `  @IsOptional()\n`;
        } else {
          content += `  @IsNotEmpty()\n`;
        }

        // Type-specific validators
        if (property.type === 'string') {
          content += `  @IsString()\n`;
          if (property.format === 'email') {
            content += `  @IsEmail()\n`;
          }
        } else if (property.type === 'number' || property.type === 'integer') {
          content += `  @IsNumber()\n`;
        } else if (property.type === 'boolean') {
          content += `  @IsBoolean()\n`;
        } else if (property.type === 'array') {
          content += `  @IsArray()\n`;
        }

        // Property declaration
        const tsType = this.getTypeScriptType(property);
        const optional = isRequired ? '' : '?';
        content += `  ${propName}${optional}: ${tsType};\n\n`;
      });
    }

    content += '}\n';
    return content;
  }

  private getTypeScriptType(schema: OpenAPIV3.SchemaObject): string {
    switch (schema.type) {
      case 'string': return 'string';
      case 'number':
      case 'integer': return 'number';
      case 'boolean': return 'boolean';
      case 'array':
        if (schema.items) {
          const itemType = this.getTypeScriptType(schema.items as OpenAPIV3.SchemaObject);
          return `${itemType}[]`;
        }
        return 'any[]';
      case 'object': return 'object';
      default: return 'any';
    }
  }

  private getModuleFromSchemaName(schemaName: string): string {
    const lower = schemaName.toLowerCase();
    if (lower.includes('auth') || lower.includes('login') || lower.includes('register') || lower.includes('token')) return 'auth';
    if (lower.includes('admin')) return 'admin';
    if (lower.includes('store') || lower.includes('cart') || lower.includes('pricing') || lower.includes('subscription')) return 'store';
    if (lower.includes('developer') || lower.includes('repository') || lower.includes('payout') || lower.includes('gitea')) return 'developer';
    if (lower.includes('webhook')) return 'webhooks';
    if (lower.includes('user')) return 'users';
    return 'users'; // Default fallback
  }

  private toProperKebabCase(str: string): string {
    // Convert PascalCase to kebab-case WITHOUT leading dash
    return str
      .replace(/([a-z0-9])([A-Z])/g, '$1-$2')
      .toLowerCase()
      .replace(/^-/, ''); // Remove any leading dash
  }

  private fixMainTsImports(): void {
    console.log('🔧 Fixing main.ts to use aliased imports...');

    const mainTsPath = path.join(this.srcDir, 'main.ts');
    if (!fs.existsSync(mainTsPath)) return;

    let content = fs.readFileSync(mainTsPath, 'utf8');

    // Replace relative imports with aliased imports
    content = content.replace(/from '\.\.\/common\//g, "from '@/common/");
    content = content.replace(/from '\.\/common\//g, "from '@/common/");
    content = content.replace(/from '\.\.\/config\//g, "from '@/config/");
    content = content.replace(/from '\.\/config\//g, "from '@/config/");

    fs.writeFileSync(mainTsPath, content);
    console.log('  ✓ Fixed main.ts imports');
  }

  private ensureDirectoryExists(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
  }
}

// Run the generator
async function main() {
  const generator = new SimpleAPIGenerator();
  await generator.generate();
}

if (require.main === module) {
  main().catch(console.error);
}
