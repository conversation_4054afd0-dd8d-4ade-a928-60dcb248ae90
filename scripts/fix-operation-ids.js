#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to automatically add operationId fields to OpenAPI specification
 * This fixes the Spectral warnings about missing operationIds
 */

const fs = require('fs');
const yaml = require('js-yaml');

// Read the OpenAPI spec
const specPath = 'api-docs/openapi.yaml';
const spec = yaml.load(fs.readFileSync(specPath, 'utf8'));

// Function to generate operationId from path and method
function generateOperationId(path, method) {
  // Remove path parameters and convert to camelCase
  const cleanPath = path
    .replace(/\{[^}]+\}/g, 'ById') // Replace {id} with ById
    .replace(/^\//, '') // Remove leading slash
    .split('/')
    .map((segment, index) => {
      if (index === 0) return segment;
      return segment.charAt(0).toUpperCase() + segment.slice(1);
    })
    .join('');

  // Add method prefix
  const methodMap = {
    get: 'get',
    post: 'create',
    put: 'update',
    patch: 'update',
    delete: 'delete'
  };

  const prefix = methodMap[method] || method;
  
  // Special cases for better naming
  if (path.includes('/login')) return 'loginUser';
  if (path.includes('/logout')) return 'logoutUser';
  if (path.includes('/register')) return 'registerUser';
  if (path.includes('/refresh')) return 'refreshToken';
  if (path.includes('/forgot-password')) return 'forgotPassword';
  if (path.includes('/reset-password')) return 'resetPassword';
  if (path.includes('/change-password')) return 'changePassword';
  if (path.includes('/2fa/setup')) return 'setup2FA';
  if (path.includes('/2fa/verify-setup')) return 'verify2FASetup';
  if (path.includes('/2fa/disable')) return 'disable2FA';
  if (path.includes('/2fa/backup-codes') && method === 'get') return 'get2FABackupCodes';
  if (path.includes('/2fa/backup-codes') && method === 'post') return 'regenerate2FABackupCodes';
  if (path.includes('/sessions') && method === 'get') return 'getUserSessions';
  if (path.includes('/sessions/revoke-all')) return 'revokeAllSessions';
  if (path.includes('/sessions/') && method === 'delete') return 'revokeSession';
  if (path.includes('/desktop/register')) return 'registerDesktopDevice';
  if (path.includes('/desktop/') && path.includes('/transfer')) return 'transferDesktopDevice';
  if (path.includes('/web/verify-device')) return 'verifyWebDevice';
  if (path.includes('/bots/sessions') && method === 'get') return 'getBotSessions';
  if (path.includes('/bots/sessions') && method === 'post') return 'startBotSession';
  if (path.includes('/bots/sessions/') && method === 'get') return 'getBotSession';
  if (path.includes('/bots/sessions/') && method === 'delete') return 'stopBotSession';
  if (path.includes('/bots/sessions/') && path.includes('/transfer')) return 'transferBotSession';
  
  return prefix + cleanPath.charAt(0).toUpperCase() + cleanPath.slice(1);
}

// Function to add missing descriptions
function generateDescription(path, method, summary) {
  if (summary && summary.length > 0) {
    return summary + ' - ' + getDescriptionSuffix(path, method);
  }
  return getDescriptionSuffix(path, method);
}

function getDescriptionSuffix(path, method) {
  const methodDescriptions = {
    get: 'Retrieve information',
    post: 'Create or perform action',
    put: 'Update information',
    patch: 'Partially update information',
    delete: 'Remove or revoke'
  };
  
  return methodDescriptions[method] || 'Perform operation';
}

// Process all paths and methods
let changesCount = 0;

for (const [path, pathItem] of Object.entries(spec.paths)) {
  for (const [method, operation] of Object.entries(pathItem)) {
    if (typeof operation === 'object' && operation !== null) {
      let changed = false;
      
      // Add operationId if missing
      if (!operation.operationId) {
        operation.operationId = generateOperationId(path, method);
        changed = true;
      }
      
      // Add description if missing
      if (!operation.description) {
        operation.description = generateDescription(path, method, operation.summary);
        changed = true;
      }
      
      if (changed) {
        changesCount++;
        console.log(`✅ Fixed ${method.toUpperCase()} ${path}: ${operation.operationId}`);
      }
    }
  }
}

// Write the updated spec back to file
fs.writeFileSync(specPath, yaml.dump(spec, {
  indent: 2,
  lineWidth: 120,
  noRefs: true,
  sortKeys: false
}));

console.log(`\n🎉 Fixed ${changesCount} operations in ${specPath}`);
console.log('✅ All operations now have operationId and description fields');
