#!/usr/bin/env ts-node

import * as fs from 'fs';
import * as path from 'path';
import * as yaml from 'js-yaml';
import { OpenAPIV3 } from 'openapi-types';

interface GenerationConfig {
  openApiPath: string;
  outputDir: string;
  templatesDir: string;
}

class NestJSCodeGenerator {
  private spec: OpenAPIV3.Document;
  private config: GenerationConfig;

  constructor(config: GenerationConfig) {
    this.config = config;
    this.loadOpenAPISpec();
  }

  private loadOpenAPISpec(): void {
    try {
      const specContent = fs.readFileSync(this.config.openApiPath, 'utf8');
      this.spec = yaml.load(specContent) as OpenAPIV3.Document;
      console.log('✅ OpenAPI specification loaded successfully');
    } catch (error) {
      console.error('❌ Failed to load OpenAPI specification:', error);
      process.exit(1);
    }
  }

  public async generateAll(): Promise<void> {
    console.log('🚀 Starting NestJS code generation...');
    
    await this.generateDTOs();
    await this.generateEntities();
    await this.generateControllers();
    await this.generateServices();
    await this.generateModules();
    
    console.log('✅ Code generation completed successfully!');
  }

  private async generateDTOs(): Promise<void> {
    console.log('📝 Generating DTOs...');
    
    if (!this.spec.components?.schemas) {
      console.log('⚠️  No schemas found in OpenAPI spec');
      return;
    }

    for (const [schemaName, schema] of Object.entries(this.spec.components.schemas)) {
      if (this.isReferenceObject(schema)) continue;
      
      const dtoContent = this.generateDTOClass(schemaName, schema);
      const fileName = this.toCamelCase(schemaName);
      const filePath = path.join(this.config.outputDir, 'dto', `${fileName}.dto.ts`);
      
      this.ensureDirectoryExists(path.dirname(filePath));
      fs.writeFileSync(filePath, dtoContent);
      console.log(`  ✓ Generated ${fileName}.dto.ts`);
    }
  }

  private generateDTOClass(name: string, schema: OpenAPIV3.SchemaObject): string {
    const className = this.toPascalCase(name);
    const imports = new Set<string>();
    imports.add("import { ApiProperty } from '@nestjs/swagger';");
    
    // Add validation imports based on schema constraints
    const validationImports = this.getValidationImports(schema);
    if (validationImports.length > 0) {
      imports.add(`import { ${validationImports.join(', ')} } from 'class-validator';`);
    }

    let classContent = `export class ${className} {\n`;

    if (schema.properties) {
      for (const [propName, propSchema] of Object.entries(schema.properties)) {
        if (this.isReferenceObject(propSchema)) continue;
        
        const property = this.generateProperty(propName, propSchema, schema.required?.includes(propName) || false);
        classContent += property;
      }
    }

    classContent += '}\n';

    return Array.from(imports).join('\n') + '\n\n' + classContent;
  }

  private generateProperty(name: string, schema: OpenAPIV3.SchemaObject, isRequired: boolean): string {
    const decorators = this.generatePropertyDecorators(name, schema, isRequired);
    const type = this.getTypeScriptType(schema);
    const optional = isRequired ? '' : '?';
    
    return `${decorators}  ${name}${optional}: ${type};\n\n`;
  }

  private generatePropertyDecorators(name: string, schema: OpenAPIV3.SchemaObject, isRequired: boolean): string {
    let decorators = '';
    
    // ApiProperty decorator
    const apiPropertyOptions: string[] = [];
    
    if (schema.description) {
      apiPropertyOptions.push(`description: '${schema.description.replace(/'/g, "\\'")}'`);
    }
    
    if (schema.example !== undefined) {
      apiPropertyOptions.push(`example: ${JSON.stringify(schema.example)}`);
    }
    
    if (schema.enum) {
      apiPropertyOptions.push(`enum: [${schema.enum.map(v => `'${v}'`).join(', ')}]`);
    }
    
    if (schema.type === 'array' && schema.items && !this.isReferenceObject(schema.items)) {
      const itemType = this.getTypeScriptType(schema.items);
      apiPropertyOptions.push(`type: [${itemType}]`);
    }
    
    if (!isRequired) {
      apiPropertyOptions.push('required: false');
    }

    decorators += `  @ApiProperty(${apiPropertyOptions.length > 0 ? `{ ${apiPropertyOptions.join(', ')} }` : ''})\n`;
    
    // Validation decorators
    const validationDecorators = this.generateValidationDecorators(schema, isRequired);
    decorators += validationDecorators;
    
    return decorators;
  }

  private generateValidationDecorators(schema: OpenAPIV3.SchemaObject, isRequired: boolean): string {
    let decorators = '';
    
    if (isRequired) {
      decorators += '  @IsNotEmpty()\n';
    } else {
      decorators += '  @IsOptional()\n';
    }
    
    switch (schema.type) {
      case 'string':
        decorators += '  @IsString()\n';
        if (schema.format === 'email') {
          decorators += '  @IsEmail()\n';
        }
        if (schema.minLength) {
          decorators += `  @MinLength(${schema.minLength})\n`;
        }
        if (schema.maxLength) {
          decorators += `  @MaxLength(${schema.maxLength})\n`;
        }
        break;
      case 'number':
      case 'integer':
        decorators += '  @IsNumber()\n';
        if (schema.minimum) {
          decorators += `  @Min(${schema.minimum})\n`;
        }
        if (schema.maximum) {
          decorators += `  @Max(${schema.maximum})\n`;
        }
        break;
      case 'boolean':
        decorators += '  @IsBoolean()\n';
        break;
      case 'array':
        decorators += '  @IsArray()\n';
        break;
    }
    
    if (schema.enum) {
      decorators += `  @IsEnum([${schema.enum.map(v => `'${v}'`).join(', ')}])\n`;
    }
    
    return decorators;
  }

  private getValidationImports(schema: OpenAPIV3.SchemaObject): string[] {
    const imports = new Set<string>();
    
    const addValidationImports = (s: OpenAPIV3.SchemaObject) => {
      imports.add('IsOptional');
      imports.add('IsNotEmpty');
      
      switch (s.type) {
        case 'string':
          imports.add('IsString');
          if (s.format === 'email') imports.add('IsEmail');
          if (s.minLength) imports.add('MinLength');
          if (s.maxLength) imports.add('MaxLength');
          break;
        case 'number':
        case 'integer':
          imports.add('IsNumber');
          if (s.minimum) imports.add('Min');
          if (s.maximum) imports.add('Max');
          break;
        case 'boolean':
          imports.add('IsBoolean');
          break;
        case 'array':
          imports.add('IsArray');
          break;
      }
      
      if (s.enum) imports.add('IsEnum');
    };

    addValidationImports(schema);
    
    if (schema.properties) {
      Object.values(schema.properties).forEach(prop => {
        if (!this.isReferenceObject(prop)) {
          addValidationImports(prop);
        }
      });
    }
    
    return Array.from(imports);
  }

  private getTypeScriptType(schema: OpenAPIV3.SchemaObject): string {
    switch (schema.type) {
      case 'string':
        return schema.enum ? schema.enum.map(v => `'${v}'`).join(' | ') : 'string';
      case 'number':
      case 'integer':
        return 'number';
      case 'boolean':
        return 'boolean';
      case 'array':
        if (schema.items && !this.isReferenceObject(schema.items)) {
          return `${this.getTypeScriptType(schema.items)}[]`;
        }
        return 'any[]';
      case 'object':
        return 'object';
      default:
        return 'any';
    }
  }

  private async generateEntities(): Promise<void> {
    console.log('🗄️  Generating entities...');
    // Entity generation logic will be implemented
  }

  private async generateControllers(): Promise<void> {
    console.log('🎮 Generating controllers...');
    // Controller generation logic will be implemented
  }

  private async generateServices(): Promise<void> {
    console.log('⚙️  Generating services...');
    // Service generation logic will be implemented
  }

  private async generateModules(): Promise<void> {
    console.log('📦 Generating modules...');
    // Module generation logic will be implemented
  }

  private isReferenceObject(obj: any): obj is OpenAPIV3.ReferenceObject {
    return obj && typeof obj === 'object' && '$ref' in obj;
  }

  private toCamelCase(str: string): string {
    return str.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
      return index === 0 ? word.toLowerCase() : word.toUpperCase();
    }).replace(/\s+/g, '');
  }

  private toPascalCase(str: string): string {
    return str.replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => {
      return word.toUpperCase();
    }).replace(/\s+/g, '');
  }

  private ensureDirectoryExists(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
  }
}

// Main execution
async function main() {
  const config: GenerationConfig = {
    openApiPath: path.join(process.cwd(), 'api-docs', 'openapi.yaml'),
    outputDir: path.join(process.cwd(), 'src', 'generated'),
    templatesDir: path.join(process.cwd(), 'scripts', 'templates'),
  };

  const generator = new NestJSCodeGenerator(config);
  await generator.generateAll();
}

if (require.main === module) {
  main().catch(console.error);
}

export { NestJSCodeGenerator };
