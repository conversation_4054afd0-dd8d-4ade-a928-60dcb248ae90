{"yaml.schemas": {"https://raw.githubusercontent.com/OAI/OpenAPI-Specification/main/schemas/v3.0/schema.json": ["*.openapi.yaml", "*.openapi.yml"]}, "yaml.validate": true, "yaml.schemaStore.enable": false, "redhat.telemetry.enabled": false, "files.associations": {"rsglider.yaml": "yaml"}, "yaml.customTags": ["!reference sequence"], "yaml.completion": true, "yaml.hover": true, "yaml.format.enable": true, "[yaml]": {"editor.defaultFormatter": "redhat.vscode-yaml"}, "swaggerViewer.defaultLang": "en", "swaggerViewer.previewInRightPanel": true, "openapi.completion": true, "openapi.validation": false}