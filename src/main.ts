import { Logger, ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import RedisStore from 'connect-redis';
import * as session from 'express-session';
import { readFileSync } from 'fs';
import * as passport from 'passport';
import { join } from 'path';
import { createClient } from 'redis';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const logger = new Logger('Bootstrap');

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // CORS configuration
  app.enableCors({
    origin: configService.get('CORS_ORIGIN', '*').split(','),
    credentials: configService.get('CORS_CREDENTIALS', 'true') === 'true',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-API-Key',
    ],
  });

  // Redis session store
  const redisClient = createClient({
    url: `redis://:${configService.get('REDIS_PASSWORD')}@${configService.get('REDIS_HOST', 'redis')}:${configService.get('REDIS_PORT', 6379)}`,
  });

  await redisClient.connect();
  logger.log('Connected to Redis');

  // Session configuration
  app.use(
    session({
      store: new RedisStore({ client: redisClient }),
      secret: configService.get('SESSION_SECRET', 'dev-secret'),
      resave: false,
      saveUninitialized: false,
      cookie: {
        maxAge: parseInt(configService.get('SESSION_MAX_AGE', '86400000')),
        httpOnly: true,
        secure: configService.get('NODE_ENV') === 'production',
      },
    }),
  );

  // Passport initialization
  app.use(passport.initialize());
  app.use(passport.session());

  // Load OpenAPI specification
  let openApiSpec;
  try {
    const openApiPath = join(process.cwd(), 'api-docs', 'openapi.yaml');
    const openApiContent = readFileSync(openApiPath, 'utf8');
    // Parse YAML to JSON (we'll add yaml parser)
    openApiSpec = openApiContent;
  } catch (error) {
    logger.warn('Could not load OpenAPI spec, using default configuration');
  }

  // Swagger configuration
  const config = new DocumentBuilder()
    .setTitle('RSGlider API')
    .setDescription('The RSGlider API for desktop and web applications')
    .setVersion('1.0.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addApiKey(
      {
        type: 'apiKey',
        name: 'X-API-Key',
        in: 'header',
        description: 'API Key for service-to-service communication',
      },
      'API-Key',
    )
    .addTag('Authentication', 'User authentication and session management')
    .addTag('User Profile', 'User profile and account management')
    .addTag('Admin', 'Administrative operations')
    .addTag('Store', 'Marketplace and store operations')
    .addTag('Developer', 'Developer tools and repository management')
    .addTag('Webhooks', 'Webhook endpoints for external integrations')
    .build();

  const documentFactory = () => SwaggerModule.createDocument(app, config, {
    operationIdFactory: (_controllerKey: string, methodKey: string) => methodKey,
    deepScanRoutes: true,
  });

  SwaggerModule.setup('api/docs', app, documentFactory, {
    customSiteTitle: 'RSGlider API Documentation',
    customfavIcon: '/favicon.ico',
    customCss: '.swagger-ui .topbar { display: none }',
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
    },
  });

  // Global prefix
  app.setGlobalPrefix('api', {
    exclude: ['/health', '/'],
  });

  // Health check endpoint
  app.use('/health', (_req, res) => {
    res.status(200).json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: configService.get('NODE_ENV'),
    });
  });

  const port = configService.get('PORT', 3000);
  await app.listen(port);

  logger.log(`🚀 Application is running on: http://localhost:${port}`);
  logger.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
  logger.log(`🏥 Health Check: http://localhost:${port}/health`);
}

bootstrap().catch((error) => {
  console.error('Failed to start application:', error);
  process.exit(1);
});
