import { Controller, Get, Post, Put, Patch, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { UsersService } from './users.service';
import { User } from './dto/user.dto';
import { UpdateUserRequest } from './dto/update-user-request.dto';
import { Role } from './dto/role.dto';
import { Permission } from './dto/permission.dto';
import { CreateRoleRequest } from './dto/create-role-request.dto';
import { UpdateRoleRequest } from './dto/update-role-request.dto';
import { CreatePermissionRequest } from './dto/create-permission-request.dto';
import { UpdatePermissionRequest } from './dto/update-permission-request.dto';
import { MarketplaceItem } from './dto/marketplace-item.dto';
import { ItemPerformance } from './dto/item-performance.dto';
import { RevenueSummary } from './dto/revenue-summary.dto';
import { BTCPaySettings } from './dto/btcpay-settings.dto';
import { OrderItem } from './dto/order-item.dto';
import { WebSession } from './dto/web-session.dto';
import { DesktopSession } from './dto/desktop-session.dto';
import { BotSession } from './dto/bot-session.dto';
import { SessionLimits } from './dto/session-limits.dto';
import { DesktopRegistrationResponse } from './dto/desktop-registration-response.dto';
import { TwoFactorSetupResponse } from './dto/two-factor-setup-response.dto';
import { TwoFactorVerifyRequest } from './dto/two-factor-verify-request.dto';
import { TwoFactorEnabledResponse } from './dto/two-factor-enabled-response.dto';
import { TwoFactorDisableRequest } from './dto/two-factor-disable-request.dto';
import { BackupCodesResponse } from './dto/backup-codes-response.dto';
import { VolumeDiscount } from './dto/volume-discount.dto';
import { AppliedDiscount } from './dto/applied-discount.dto';
import { AppliedCoupon } from './dto/applied-coupon.dto';
import { FeatureAccessCheck } from './dto/feature-access-check.dto';
import { UpgradeOption } from './dto/upgrade-option.dto';
import { Pagination } from './dto/pagination.dto';
import { Error } from './dto/error.dto';

@ApiTags('User Profile')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('/me')
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ status: 200, description: 'Success' })
  async getCurrentUser() {
    return this.usersService.getCurrentUser();
  }

  @Put('/me')
  @ApiOperation({ summary: 'Update current user profile' })
  @ApiResponse({ status: 200, description: 'Success' })
  async updateCurrentUser(@Body() body: any) {
    return this.usersService.updateCurrentUser(body);
  }
}
