import { Controller } from '@nestjs/common';
import { Api<PERSON><PERSON><PERSON>, <PERSON>pi<PERSON>earerAuth } from '@nestjs/swagger';
import { AdminService } from './admin.service';
import { Roles } from '../../common/decorators/roles.decorator';
import { UserRole } from '../../common/enums/user-role.enum';

@ApiTags('Admin')
@ApiBearerAuth('JWT-auth')
@Roles(UserRole.ADMIN)
@Controller('admin')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  // Admin endpoints will be auto-generated from OpenAPI spec
}
