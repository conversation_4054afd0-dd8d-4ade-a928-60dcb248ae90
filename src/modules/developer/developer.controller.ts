import { Controller } from '@nestjs/common';
import { Api<PERSON><PERSON><PERSON>, <PERSON>pi<PERSON><PERSON>erA<PERSON> } from '@nestjs/swagger';
import { DeveloperService } from './developer.service';
import { Roles } from '../../common/decorators/roles.decorator';
import { UserRole } from '../../common/enums/user-role.enum';

@ApiTags('Developer')
@ApiBearerAuth('JWT-auth')
@Roles(UserRole.DEVELOPER)
@Controller('developer')
export class DeveloperController {
  constructor(private readonly developerService: DeveloperService) {}

  // Developer endpoints will be auto-generated from OpenAPI spec
}
