import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>E<PERSON>, IsNotEmpty, IsString, <PERSON><PERSON><PERSON>th, <PERSON><PERSON>ength } from 'class-validator';

export class RegisterDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'User password',
    example: 'SecurePassword123!',
    minLength: 8,
    maxLength: 128,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  @MaxLength(128)
  password: string;

  @ApiProperty({
    description: 'User display name',
    example: '<PERSON>',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  name?: string;
}
