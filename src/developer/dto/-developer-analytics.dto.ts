import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsEnum, IsArray, IsUUID, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> } from 'class-validator';

export class DeveloperAnalytics {
  @ApiProperty({ enum: ['7d', '30d', '90d', '1y', 'all'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['7d', '30d', '90d', '1y', 'all'])
  period: '7d' | '30d' | '90d' | '1y' | 'all'?;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  totalRevenue: number?;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  totalDownloads: number?;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  totalViews: number?;

  @ApiProperty({ description: 'Number of published marketplace items', required: false })
  @IsOptional()
  @IsNumber()
  activeItems: number?;

  @ApiProperty({ type: [Object], required: false })
  @IsOptional()
  @IsArray()
  topPerformingItems: any[]?;

  @ApiProperty({ type: [Object], required: false })
  @IsOptional()
  @IsArray()
  revenueByPeriod: object[]?;

  @ApiProperty({ required: false })
  @IsOptional()
  categoryBreakdown: object?;

}
