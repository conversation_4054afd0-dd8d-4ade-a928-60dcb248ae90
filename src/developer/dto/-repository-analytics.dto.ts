import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsEnum, IsArray, IsUUID, <PERSON><PERSON>ength, <PERSON>ength, <PERSON>, Max } from 'class-validator';

export class RepositoryAnalytics {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  repositoryId: string?;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  period: string?;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  revenue: number?;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  downloads: number?;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  views: number?;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  uniqueUsers: number?;

  @ApiProperty({ description: 'Views to downloads conversion rate', required: false })
  @IsOptional()
  @IsNumber()
  conversionRate: number?;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  rating: number?;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  reviewCount: number?;

  @ApiProperty({ type: [Object], required: false })
  @IsOptional()
  @IsArray()
  downloadsByPeriod: object[]?;

  @ApiProperty({ required: false })
  @IsOptional()
  geographicBreakdown: object?;

  @ApiProperty({ required: false })
  @IsOptional()
  referralSources: object?;

}
