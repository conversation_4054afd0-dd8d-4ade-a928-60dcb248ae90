import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { DeveloperService } from './developer.service';

@ApiTags('developer')
@Controller('developer')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class DeveloperController {
  constructor(private readonly developerService: DeveloperService) {}

  @Get()
  @ApiOperation({ summary: 'Get all developer' })
  @ApiResponse({ status: 200, description: 'Success' })
  async findAll(@Query() query: any) {
    return this.developerService.findAll(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get developer by ID' })
  @ApiResponse({ status: 200, description: 'Success' })
  async findOne(@Param('id') id: string) {
    return this.developerService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create developer' })
  @ApiResponse({ status: 201, description: 'Created' })
  async create(@Body() createDto: any) {
    return this.developerService.create(createDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update developer' })
  @ApiResponse({ status: 200, description: 'Updated' })
  async update(@Param('id') id: string, @Body() updateDto: any) {
    return this.developerService.update(id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete developer' })
  @ApiResponse({ status: 200, description: 'Deleted' })
  async remove(@Param('id') id: string) {
    return this.developerService.remove(id);
  }
}
