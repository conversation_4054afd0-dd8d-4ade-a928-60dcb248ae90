import { Controller, Get, Post, Put, Patch, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { AuthService } from './auth.service';
import { RegisterRequest } from './dto/register-request.dto';
import { LoginRequest } from './dto/login-request.dto';
import { AuthResponse } from './dto/auth-response.dto';
import { TokenResponse } from './dto/token-response.dto';

@ApiTags('Authentication')
@Controller('auth')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('/register')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({ status: 200, description: 'Success' })
  async registerUser(@Body() body: any) {
    return this.authService.registerUser(body);
  }

  @Post('/login')
  @ApiOperation({ summary: 'Login user' })
  @ApiResponse({ status: 200, description: 'Success' })
  async loginUser(@Body() body: any) {
    return this.authService.loginUser(body);
  }

  @Post('/logout')
  @ApiOperation({ summary: 'Logout user (invalidate current session)' })
  @ApiResponse({ status: 200, description: 'Success' })
  async logoutUser() {
    return this.authService.logoutUser();
  }

  @Post('/refresh')
  @ApiOperation({ summary: 'Refresh access token using refresh token' })
  @ApiResponse({ status: 200, description: 'Success' })
  async refreshToken(@Body() body: any) {
    return this.authService.refreshToken(body);
  }

  @Post('/forgot-password')
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({ status: 200, description: 'Success' })
  async forgotPassword(@Body() body: any) {
    return this.authService.forgotPassword(body);
  }

  @Post('/reset-password')
  @ApiOperation({ summary: 'Reset password with token' })
  @ApiResponse({ status: 200, description: 'Success' })
  async resetPassword(@Body() body: any) {
    return this.authService.resetPassword(body);
  }
}
