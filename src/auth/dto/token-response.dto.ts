import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class TokenResponse {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  accessToken?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  refreshToken?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  expiresIn?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  tokenType?: string;

}
