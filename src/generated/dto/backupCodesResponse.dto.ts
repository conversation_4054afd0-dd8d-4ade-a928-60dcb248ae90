import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsArray, IsString, IsNumber } from 'class-validator';

export class BackupCodesResponse {
  @ApiProperty({ description: 'List of 8-character backup codes', type: [String], required: false })
  @IsOptional()
  @IsArray()
  codes?: string[];

  @ApiProperty({ description: 'When these codes were generated', required: false })
  @IsOptional()
  @IsString()
  generatedAt?: string;

  @ApiProperty({ description: 'List of already used backup codes (masked)', type: [String], required: false })
  @IsOptional()
  @IsArray()
  usedCodes?: string[];

  @ApiProperty({ description: 'Number of unused backup codes', required: false })
  @IsOptional()
  @IsNumber()
  remainingCodes?: number;

}
