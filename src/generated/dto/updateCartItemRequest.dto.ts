import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsNumber, Min, IsString, IsEnum } from 'class-validator';

export class UpdateCartItemRequest {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  instances?: number;

  @ApiProperty({ enum: ['monthly', 'quarterly', 'annual'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['monthly', 'quarterly', 'annual'])
  duration?: 'monthly' | 'quarterly' | 'annual';

}
