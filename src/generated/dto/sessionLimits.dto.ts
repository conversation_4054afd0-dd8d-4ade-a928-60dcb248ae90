import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEnum, IsBoolean } from 'class-validator';

export class SessionLimits {
  @ApiProperty({ required: false })
  @IsOptional()
  webSessions?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  desktopSessions?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  botSessions?: object;

  @ApiProperty({ description: 'Current subscription tier', enum: ['free', 'pro'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['free', 'pro'])
  subscriptionTier?: 'free' | 'pro';

  @ApiProperty({ description: 'Active addon purchases', example: {"desktopAddons":2,"botAddons":5}, required: false })
  @IsOptional()
  addons?: object;

  @ApiProperty({ description: 'Whether upgrade is needed for more sessions', required: false })
  @IsOptional()
  @IsBoolean()
  upgradeRequired?: boolean;

}
