import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEnum, IsBoolean, IsNumber } from 'class-validator';

export class DesktopSession {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ enum: ['desktop'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['desktop'])
  platform?: 'desktop';

  @ApiProperty({ required: false })
  @IsOptional()
  deviceInfo?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  registeredAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  lastActivityAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isCurrent?: boolean;

  @ApiProperty({ description: 'Number of active bot sessions on this device', required: false })
  @IsOptional()
  @IsNumber()
  activeBotSessions?: number;

  @ApiProperty({ description: 'Maximum bot sessions allowed on this device', required: false })
  @IsOptional()
  @IsNumber()
  maxBotSessions?: number;

  @ApiProperty({ description: 'Whether this device can be transferred to another', required: false })
  @IsOptional()
  @IsBoolean()
  transferable?: boolean;

}
