import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsArray, IsEnum, IsBoolean } from 'class-validator';

export class PublishRepositoryRequest {
  @ApiProperty({ description: 'Marketplace category for the item' })
  @IsNotEmpty()
  @IsString()
  category: string;

  @ApiProperty({ description: 'Tags for better discoverability', type: [String], required: false })
  @IsOptional()
  @IsArray()
  tags?: string[];

  @ApiProperty({ description: 'Marketplace visibility', enum: ['public', 'private'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['public', 'private'])
  visibility?: 'public' | 'private';

  @ApiProperty({ description: 'Request featured placement (admin approval required)', required: false })
  @IsOptional()
  @IsBoolean()
  featured?: boolean;

  @ApiProperty({ description: 'Override repository description for marketplace', required: false })
  @IsOptional()
  @IsString()
  customDescription?: string;

  @ApiProperty({ description: 'Override repository name for marketplace', required: false })
  @IsOptional()
  @IsString()
  customName?: string;

}
