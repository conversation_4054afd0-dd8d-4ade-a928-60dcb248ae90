import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsArray, IsNumber, IsEnum } from 'class-validator';

export class Cart {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  items?: any[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  subtotal?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  discounts?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  taxes?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  total?: number;

  @ApiProperty({ enum: ['USD', 'EUR', 'GBP'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['USD', 'EUR', 'GBP'])
  currency?: 'USD' | 'EUR' | 'GBP';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  appliedCoupons?: any[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  estimatedSavings?: number;

}
