import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEnum, IsBoolean } from 'class-validator';

export class WebSession {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ enum: ['web'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['web'])
  platform?: 'web';

  @ApiProperty({ required: false })
  @IsOptional()
  deviceInfo?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  location?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  createdAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  lastActivityAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  expiresAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isCurrent?: boolean;

  @ApiProperty({ description: 'Whether this session requires device verification', required: false })
  @IsOptional()
  @IsBoolean()
  requiresVerification?: boolean;

}
