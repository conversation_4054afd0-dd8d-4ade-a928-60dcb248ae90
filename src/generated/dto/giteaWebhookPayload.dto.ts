import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEnum, IsArray } from 'class-validator';

export class GiteaWebhookPayload {
  @ApiProperty({ enum: ['created', 'deleted', 'pushed', 'released'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['created', 'deleted', 'pushed', 'released'])
  action?: 'created' | 'deleted' | 'pushed' | 'released';

  @ApiProperty({ required: false })
  @IsOptional()
  repository?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  pusher?: object;

  @ApiProperty({ type: [Object], required: false })
  @IsOptional()
  @IsArray()
  commits?: object[];

  @ApiProperty({ required: false })
  @IsOptional()
  release?: object;

}
