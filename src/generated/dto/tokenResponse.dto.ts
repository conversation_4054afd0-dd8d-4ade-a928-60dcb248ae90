import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsNumber, IsEnum } from 'class-validator';

export class TokenResponse {
  @ApiProperty({ description: 'JWT access token (typically expires in 15-30 minutes)', required: false })
  @IsOptional()
  @IsString()
  accessToken?: string;

  @ApiProperty({ description: 'Refresh token for obtaining new access tokens (rotated on each use)', required: false })
  @IsOptional()
  @IsString()
  refreshToken?: string;

  @ApiProperty({ description: 'Access token expiration time in seconds', required: false })
  @IsOptional()
  @IsNumber()
  expiresIn?: number;

  @ApiProperty({ enum: ['Bearer'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['Bearer'])
  tokenType?: 'Bearer';

}
