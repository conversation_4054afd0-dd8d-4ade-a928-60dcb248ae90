import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsNumber, IsString, IsBoolean } from 'class-validator';

export class DeveloperRepository {
  @ApiProperty({ description: 'Gitea repository ID', required: false })
  @IsOptional()
  @IsNumber()
  id?: number;

  @ApiProperty({ description: 'Repository name', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: 'Full repository name (owner/repo)', required: false })
  @IsOptional()
  @IsString()
  fullName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  private?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  fork?: boolean;

  @ApiProperty({ description: 'Repository size in KB', required: false })
  @IsOptional()
  @IsNumber()
  size?: number;

  @ApiProperty({ description: 'Primary programming language', required: false })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  stars?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  forks?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  openIssues?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  defaultBranch?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  createdAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  updatedAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  pushedAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  cloneUrl?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sshUrl?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  htmlUrl?: string;

  @ApiProperty({ description: 'Whether this repo is published to marketplace', required: false })
  @IsOptional()
  @IsBoolean()
  isPublished?: boolean;

  @ApiProperty({ description: 'Associated marketplace item ID if published', required: false })
  @IsOptional()
  @IsString()
  marketplaceItemId?: string;

  @ApiProperty({ description: 'Whether repo contains required marketplace metadata', required: false })
  @IsOptional()
  @IsBoolean()
  hasMarketplaceMetadata?: boolean;

}
