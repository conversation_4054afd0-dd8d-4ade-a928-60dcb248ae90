import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsBoolean, IsEnum, IsArray, IsString } from 'class-validator';

export class TwoFactorEnabledResponse {
  @ApiProperty({ enum: ['true'], required: false })
  @IsOptional()
  @IsBoolean()
  @IsEnum(['true'])
  enabled?: boolean;

  @ApiProperty({ description: 'One-time backup codes for account recovery', type: [String], required: false })
  @IsOptional()
  @IsArray()
  backupCodes?: string[];

  @ApiProperty({ example: "Two-factor authentication has been successfully enabled", required: false })
  @IsOptional()
  @IsString()
  message?: string;

}
