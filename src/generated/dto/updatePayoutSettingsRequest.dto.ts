import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsNumber, Min, IsString, IsEnum, IsBoolean, Max } from 'class-validator';

export class UpdatePayoutSettingsRequest {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  minimumAmount?: number;

  @ApiProperty({ enum: ['daily', 'weekly', 'biweekly', 'monthly', 'quarterly'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['daily', 'weekly', 'biweekly', 'monthly', 'quarterly'])
  frequency?: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'quarterly';

  @ApiProperty({ required: false })
  @IsOptional()
  revenueShare?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  autoPayoutEnabled?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Max(365)
  holdingPeriod?: number;

}
