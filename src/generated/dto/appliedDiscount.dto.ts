import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEnum, IsNumber } from 'class-validator';

export class AppliedDiscount {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ enum: ['volume', 'duration', 'bundle', 'coupon'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['volume', 'duration', 'bundle', 'coupon'])
  type?: 'volume' | 'duration' | 'bundle' | 'coupon';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  amount?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  percentage?: number;

}
