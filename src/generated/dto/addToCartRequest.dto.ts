import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsNumber, Min, IsEnum } from 'class-validator';

export class AddToCartRequest {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  itemId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  planId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  instances?: number;

  @ApiProperty({ enum: ['monthly', 'quarterly', 'annual'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['monthly', 'quarterly', 'annual'])
  duration?: 'monthly' | 'quarterly' | 'annual';

}
