import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsNumber } from 'class-validator';

export class ItemPerformance {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  itemId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  revenue?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  downloads?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  views?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  conversionRate?: number;

}
