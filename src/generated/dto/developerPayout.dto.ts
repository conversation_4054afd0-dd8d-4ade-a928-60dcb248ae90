import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsNumber, IsEnum, IsArray } from 'class-validator';

export class DeveloperPayout {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  amount?: number;

  @ApiProperty({ enum: ['USD', 'EUR', 'GBP'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['USD', 'EUR', 'GBP'])
  currency?: 'USD' | 'EUR' | 'GBP';

  @ApiProperty({ enum: ['pending', 'processing', 'completed', 'failed'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['pending', 'processing', 'completed', 'failed'])
  status?: 'pending' | 'processing' | 'completed' | 'failed';

  @ApiProperty({ enum: ['btcpay_bitcoin'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['btcpay_bitcoin'])
  paymentMethod?: 'btcpay_bitcoin';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  transactionId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  period?: object;

  @ApiProperty({ type: [Object], required: false })
  @IsOptional()
  @IsArray()
  itemsIncluded?: object[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  createdAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  processedAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  failureReason?: string;

}
