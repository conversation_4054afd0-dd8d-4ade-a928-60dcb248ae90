import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, <PERSON><PERSON>ength, MaxLength, IsArray } from 'class-validator';

export class CreateRoleRequest {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  @MaxLength(255)
  description: string;

  @ApiProperty({ description: 'Array of permission IDs to assign to this role', type: [String], required: false })
  @IsOptional()
  @IsArray()
  permissions?: string[];

}
