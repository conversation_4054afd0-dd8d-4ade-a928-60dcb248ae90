import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEnum } from 'class-validator';

export class BTCPaySettings {
  @ApiProperty({ description: 'BTCPay Server URL', required: false })
  @IsOptional()
  @IsString()
  serverUrl?: string;

  @ApiProperty({ description: 'BTCPay Store ID for payouts', required: false })
  @IsOptional()
  @IsString()
  storeId?: string;

  @ApiProperty({ description: 'Webhook secret for BTCPay notifications', required: false })
  @IsOptional()
  @IsString()
  webhookSecret?: string;

  @ApiProperty({ description: 'Who pays Bitcoin network fees', enum: ['platform_pays', 'developer_pays', 'split'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['platform_pays', 'developer_pays', 'split'])
  networkFeePolicy?: 'platform_pays' | 'developer_pays' | 'split';

}
