import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsNumber, IsEnum, IsBoolean } from 'class-validator';

export class OrderItem {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  itemId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  itemName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  developerId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  developerName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  quantity?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  unitPrice?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  totalPrice?: number;

  @ApiProperty({ enum: ['USD', 'EUR', 'GBP'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['USD', 'EUR', 'GBP'])
  currency?: 'USD' | 'EUR' | 'GBP';

  @ApiProperty({ description: 'Download URL (available after payment)', required: false })
  @IsOptional()
  @IsString()
  downloadUrl?: string;

  @ApiProperty({ description: 'License key (if applicable)', required: false })
  @IsOptional()
  @IsString()
  licenseKey?: string;

  @ApiProperty({ description: 'Whether access has been granted to the item', required: false })
  @IsOptional()
  @IsBoolean()
  accessGranted?: boolean;

}
