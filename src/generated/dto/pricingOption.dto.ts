import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsNumber, IsEnum } from 'class-validator';

export class PricingOption {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  planId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  instances?: number;

  @ApiProperty({ enum: ['monthly', 'quarterly', 'annual'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['monthly', 'quarterly', 'annual'])
  duration?: 'monthly' | 'quarterly' | 'annual';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  originalPrice?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  finalPrice?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  savings?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  savingsPercentage?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  priceBreakdown?: object;

}
