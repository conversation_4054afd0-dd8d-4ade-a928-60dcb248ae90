import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEnum, IsNumber, IsArray, IsBoolean } from 'class-validator';

export class SubscriptionPlan {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ enum: ['monthly', 'quarterly', 'annual'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['monthly', 'quarterly', 'annual'])
  duration?: 'monthly' | 'quarterly' | 'annual';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  price?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  originalPrice?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  discountPercentage?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  maxInstances?: number;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  features?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  popular?: boolean;

}
