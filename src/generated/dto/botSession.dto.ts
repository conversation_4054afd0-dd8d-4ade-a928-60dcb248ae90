import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEnum, IsBoolean } from 'class-validator';

export class BotSession {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ description: 'User-friendly name for this bot session', required: false })
  @IsOptional()
  @IsString()
  sessionName?: string;

  @ApiProperty({ description: 'Type of bot running', required: false })
  @IsOptional()
  @IsString()
  botType?: string;

  @ApiProperty({ description: 'Associated desktop session', required: false })
  @IsOptional()
  @IsString()
  desktopSessionId?: string;

  @ApiProperty({ description: 'Device where bot is running', required: false })
  @IsOptional()
  @IsString()
  deviceName?: string;

  @ApiProperty({ enum: ['starting', 'running', 'paused', 'stopping', 'stopped', 'error'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['starting', 'running', 'paused', 'stopping', 'stopped', 'error'])
  status?: 'starting' | 'running' | 'paused' | 'stopping' | 'stopped' | 'error';

  @ApiProperty({ description: 'Bot-specific configuration', required: false })
  @IsOptional()
  configuration?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  statistics?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  lastActivityAt?: string;

  @ApiProperty({ description: 'Whether this bot session can be transferred', required: false })
  @IsOptional()
  @IsBoolean()
  canTransfer?: boolean;

  @ApiProperty({ description: 'Subscription tier that allows this bot', required: false })
  @IsOptional()
  @IsString()
  subscriptionTier?: string;

}
