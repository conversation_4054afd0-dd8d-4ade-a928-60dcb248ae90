import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsArray } from 'class-validator';

export class StoreFilters {
  @ApiProperty({ type: [Object], required: false })
  @IsOptional()
  @IsArray()
  categories?: object[];

  @ApiProperty({ type: [Object], required: false })
  @IsOptional()
  @IsArray()
  priceRanges?: object[];

  @ApiProperty({ type: [Object], required: false })
  @IsOptional()
  @IsArray()
  tags?: object[];

}
