import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsBoolean, IsEnum, IsString, IsNumber } from 'class-validator';

export class DesktopRegistrationResponse {
  @ApiProperty({ enum: ['true'], required: false })
  @IsOptional()
  @IsBoolean()
  @IsEnum(['true'])
  registered?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sessionId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  deviceId?: string;

  @ApiProperty({ description: 'Bot sessions allowed on this device', required: false })
  @IsOptional()
  @IsNumber()
  maxBotSessions?: number;

  @ApiProperty({ description: 'Device transfers remaining this period', required: false })
  @IsOptional()
  @IsNumber()
  transfersRemaining?: number;

}
