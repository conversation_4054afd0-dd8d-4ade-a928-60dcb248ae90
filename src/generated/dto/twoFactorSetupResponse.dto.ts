import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsArray } from 'class-validator';

export class TwoFactorSetupResponse {
  @ApiProperty({ description: 'Base32-encoded TOTP secret for manual entry', required: false })
  @IsOptional()
  @IsString()
  secret?: string;

  @ApiProperty({ description: 'Data URL for QR code image', required: false })
  @IsOptional()
  @IsString()
  qrCodeUrl?: string;

  @ApiProperty({ description: 'One-time backup codes (only shown during setup)', type: [String], required: false })
  @IsOptional()
  @IsArray()
  backupCodes?: string[];

  @ApiProperty({ description: 'Human-readable secret key for manual entry', required: false })
  @IsOptional()
  @IsString()
  manualEntryKey?: string;

  @ApiProperty({ description: 'Service name for the authenticator app', example: "RSGlider", required: false })
  @IsOptional()
  @IsString()
  issuer?: string;

}
