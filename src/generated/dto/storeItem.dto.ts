import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsArray, IsBoolean, IsNumber, Max, IsEnum } from 'class-validator';

export class StoreItem {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  shortDescription?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  tags?: string[];

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  images?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  featured?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Max(5)
  rating?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  reviewCount?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  basePrice?: number;

  @ApiProperty({ enum: ['USD', 'EUR', 'GBP'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['USD', 'EUR', 'GBP'])
  currency?: 'USD' | 'EUR' | 'GBP';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isFree?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  hasInstanceLimits?: boolean;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  featureFlags?: string[];

  @ApiProperty({ enum: ['published', 'draft', 'archived'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['published', 'draft', 'archived'])
  status?: 'published' | 'draft' | 'archived';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  createdAt?: string;

}
