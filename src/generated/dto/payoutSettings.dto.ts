import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsNumber, IsString, IsEnum, IsBoolean } from 'class-validator';

export class PayoutSettings {
  @ApiProperty({ description: 'Minimum payout amount in USD', required: false })
  @IsOptional()
  @IsNumber()
  minimumAmount?: number;

  @ApiProperty({ description: 'How often payouts are processed', enum: ['daily', 'weekly', 'biweekly', 'monthly', 'quarterly'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['daily', 'weekly', 'biweekly', 'monthly', 'quarterly'])
  frequency?: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'quarterly';

  @ApiProperty({ enum: ['btcpay_bitcoin'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['btcpay_bitcoin'])
  paymentMethod?: 'btcpay_bitcoin';

  @ApiProperty({ required: false })
  @IsOptional()
  revenueShare?: object;

  @ApiProperty({ description: 'Whether to automatically process payouts', required: false })
  @IsOptional()
  @IsBoolean()
  autoPayoutEnabled?: boolean;

  @ApiProperty({ description: 'Days to hold revenue before payout eligibility', required: false })
  @IsOptional()
  @IsNumber()
  holdingPeriod?: number;

  @ApiProperty({ enum: ['USD', 'EUR', 'GBP'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['USD', 'EUR', 'GBP'])
  currency?: 'USD' | 'EUR' | 'GBP';

}
