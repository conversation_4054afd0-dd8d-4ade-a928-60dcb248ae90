import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsNumber, IsEnum, IsArray } from 'class-validator';

export class CartItem {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  itemId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  itemName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  planId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  planName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  instances?: number;

  @ApiProperty({ enum: ['monthly', 'quarterly', 'annual'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['monthly', 'quarterly', 'annual'])
  duration?: 'monthly' | 'quarterly' | 'annual';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  unitPrice?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  totalPrice?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  discounts?: any[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  addedAt?: string;

}
