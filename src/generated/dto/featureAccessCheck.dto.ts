import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsBoolean, IsEnum, IsNumber, IsArray } from 'class-validator';

export class FeatureAccessCheck {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  featureKey?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  hasAccess?: boolean;

  @ApiProperty({ enum: ['granted', 'no_subscription', 'expired', 'instance_limit_exceeded', 'feature_disabled'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['granted', 'no_subscription', 'expired', 'instance_limit_exceeded', 'feature_disabled'])
  reason?: 'granted' | 'no_subscription' | 'expired' | 'instance_limit_exceeded' | 'feature_disabled';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  maxInstances?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  availableInstances?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  nextAvailableAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  upgradeOptions?: any[];

}
