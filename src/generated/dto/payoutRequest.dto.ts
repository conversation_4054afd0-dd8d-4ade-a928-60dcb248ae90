import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsNumber, IsEnum } from 'class-validator';

export class PayoutRequest {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  developerId?: string;

  @ApiProperty({ description: 'Requested amount in USD', required: false })
  @IsOptional()
  @IsNumber()
  amount?: number;

  @ApiProperty({ description: 'Amount in Bitcoin at time of request', required: false })
  @IsOptional()
  @IsNumber()
  btcAmount?: number;

  @ApiProperty({ description: 'Bitcoin address for payout', required: false })
  @IsOptional()
  @IsString()
  btcAddress?: string;

  @ApiProperty({ description: 'USD to Bitcoin rate at time of request', required: false })
  @IsOptional()
  @IsNumber()
  btcRate?: number;

  @ApiProperty({ enum: ['pending', 'approved', 'processing', 'completed', 'failed', 'cancelled'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['pending', 'approved', 'processing', 'completed', 'failed', 'cancelled'])
  status?: 'pending' | 'approved' | 'processing' | 'completed' | 'failed' | 'cancelled';

  @ApiProperty({ enum: ['manual', 'automatic'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['manual', 'automatic'])
  requestType?: 'manual' | 'automatic';

  @ApiProperty({ description: 'Reason for manual request', required: false })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  requestedAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  approvedAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  processedAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  completedAt?: string;

  @ApiProperty({ description: 'BTCPay Server invoice ID for the payout', required: false })
  @IsOptional()
  @IsString()
  btcpayInvoiceId?: string;

  @ApiProperty({ description: 'Bitcoin transaction ID', required: false })
  @IsOptional()
  @IsString()
  transactionId?: string;

  @ApiProperty({ description: 'Bitcoin network fee paid', required: false })
  @IsOptional()
  @IsNumber()
  networkFee?: number;

  @ApiProperty({ description: 'Reason for failure if status is failed', required: false })
  @IsOptional()
  @IsString()
  failureReason?: string;

}
