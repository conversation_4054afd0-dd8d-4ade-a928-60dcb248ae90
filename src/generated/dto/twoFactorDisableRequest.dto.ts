import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString } from 'class-validator';

export class TwoFactorDisableRequest {
  @ApiProperty({ description: 'Current user password for verification' })
  @IsNotEmpty()
  @IsString()
  password: string;

  @ApiProperty({ description: 'Either 6-digit TOTP code or backup code', required: false })
  @IsOptional()
  @IsString()
  code?: string;

}
