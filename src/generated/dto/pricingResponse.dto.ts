import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsNumber, IsEnum, IsArray } from 'class-validator';

export class PricingResponse {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  itemId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  requestedInstances?: number;

  @ApiProperty({ enum: ['monthly', 'quarterly', 'annual'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['monthly', 'quarterly', 'annual'])
  requestedDuration?: 'monthly' | 'quarterly' | 'annual';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  options?: any[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  appliedDiscounts?: any[];

}
