import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEnum, IsNumber } from 'class-validator';

export class VolumeDiscount {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  conditions?: object;

  @ApiProperty({ enum: ['percentage', 'fixed_amount', 'buy_x_get_y'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['percentage', 'fixed_amount', 'buy_x_get_y'])
  discountType?: 'percentage' | 'fixed_amount' | 'buy_x_get_y';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  discountValue?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  maxDiscount?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  validFrom?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  validUntil?: string;

}
