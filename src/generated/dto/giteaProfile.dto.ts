import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsNumber, IsString, IsEmail, IsEnum, IsBoolean } from 'class-validator';

export class GiteaProfile {
  @ApiProperty({ description: 'Gitea user ID', required: false })
  @IsOptional()
  @IsNumber()
  id?: number;

  @ApiProperty({ description: 'Gitea username', required: false })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiProperty({ description: 'User\'s full name in Gitea', required: false })
  @IsOptional()
  @IsString()
  fullName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @IsEmail()
  email?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  avatarUrl?: string;

  @ApiProperty({ description: 'Direct link to git.rsglider.com profile', required: false })
  @IsOptional()
  @IsString()
  profileUrl?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  publicRepos?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  privateRepos?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  totalRepos?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  followers?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  following?: number;

  @ApiProperty({ description: 'When the Gitea account was provisioned', required: false })
  @IsOptional()
  @IsString()
  provisionedAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  lastSyncAt?: string;

  @ApiProperty({ enum: ['active', 'syncing', 'error', 'disabled'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['active', 'syncing', 'error', 'disabled'])
  syncStatus?: 'active' | 'syncing' | 'error' | 'disabled';

  @ApiProperty({ enum: ['active', 'disabled', 'archived'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['active', 'disabled', 'archived'])
  accountStatus?: 'active' | 'disabled' | 'archived';

  @ApiProperty({ description: 'Whether SSO login is enabled', required: false })
  @IsOptional()
  @IsBoolean()
  ssoEnabled?: boolean;

  @ApiProperty({ description: 'Whether account was provisioned by admin', required: false })
  @IsOptional()
  @IsBoolean()
  adminProvisioned?: boolean;

  @ApiProperty({ description: 'Admin username who provisioned the account', required: false })
  @IsOptional()
  @IsString()
  provisionedBy?: string;

}
