import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, MinLength, MaxLength } from 'class-validator';

export class CreatePermissionRequest {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  name: string;

  @ApiProperty({ description: 'The resource this permission applies to (e.g., \'users\', \'roles\', \'store\')' })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  resource: string;

  @ApiProperty({ description: 'The action this permission allows (e.g., \'read\', \'write\', \'delete\')' })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  action: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  @MaxLength(255)
  description: string;

}
