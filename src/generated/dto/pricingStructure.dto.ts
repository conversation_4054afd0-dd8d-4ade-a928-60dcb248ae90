import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsNumber, IsString, IsEnum, IsBoolean, IsArray } from 'class-validator';

export class PricingStructure {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  basePrice?: number;

  @ApiProperty({ enum: ['USD', 'EUR', 'GBP'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['USD', 'EUR', 'GBP'])
  currency?: 'USD' | 'EUR' | 'GBP';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isFree?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  subscriptionPlans?: any[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  instancePricing?: any[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  volumeDiscounts?: any[];

}
