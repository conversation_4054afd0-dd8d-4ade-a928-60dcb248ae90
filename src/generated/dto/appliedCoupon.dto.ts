import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsNumber, IsEnum } from 'class-validator';

export class AppliedCoupon {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  discountAmount?: number;

  @ApiProperty({ enum: ['percentage', 'fixed_amount'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['percentage', 'fixed_amount'])
  discountType?: 'percentage' | 'fixed_amount';

}
