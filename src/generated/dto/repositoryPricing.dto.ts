import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEnum, IsNumber, IsArray } from 'class-validator';

export class RepositoryPricing {
  @ApiProperty({ enum: ['free', 'one_time', 'subscription', 'pay_what_you_want'] })
  @IsNotEmpty()
  @IsString()
  @IsEnum(['free', 'one_time', 'subscription', 'pay_what_you_want'])
  type: 'free' | 'one_time' | 'subscription' | 'pay_what_you_want';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  basePrice?: number;

  @ApiProperty({ enum: ['USD', 'EUR', 'GBP'], required: false })
  @IsOptional()
  @IsString()
  @IsEnum(['USD', 'EUR', 'GBP'])
  currency?: 'USD' | 'EUR' | 'GBP';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  subscriptionPlans?: any[];

  @ApiProperty({ description: 'For pay_what_you_want pricing', required: false })
  @IsOptional()
  @IsNumber()
  minimumPrice?: number;

  @ApiProperty({ description: 'For pay_what_you_want pricing', required: false })
  @IsOptional()
  @IsNumber()
  suggestedPrice?: number;

}
