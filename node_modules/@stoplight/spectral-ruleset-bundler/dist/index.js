"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bundleRuleset = void 0;
const rollup_1 = require("rollup");
const path_1 = require("@stoplight/path");
const isPackageImport_1 = require("./utils/isPackageImport");
const dedupeRollupPlugins_1 = require("./utils/dedupeRollupPlugins");
async function bundleRuleset(input, { target = 'browser', plugins, format, treeshake = false, fullOutput = false }) {
    const bundle = await (0, rollup_1.rollup)({
        input,
        plugins: (0, dedupeRollupPlugins_1.dedupeRollupPlugins)(plugins),
        treeshake,
        watch: false,
        perf: false,
        onwarn(e, fn) {
            if (e.code === 'MISSING_NAME_OPTION_FOR_IIFE_EXPORT') {
                return;
            }
            if (e.code === 'UNRESOLVED_IMPORT' &&
                typeof e.source === 'string' &&
                e.source.startsWith('@stoplight/spectral')) {
                return;
            }
            fn(e);
        },
        external: target === 'runtime'
            ? []
            : target === 'browser'
                ? id => (0, path_1.isURL)(id)
                : (id, importer) => id.startsWith('node:') || (!(0, path_1.isURL)(id) && (0, isPackageImport_1.isPackageImport)(id) && (importer === void 0 || !(0, path_1.isURL)(importer))),
    });
    const chunks = (await bundle.generate({ format: format !== null && format !== void 0 ? format : (target === 'runtime' ? 'iife' : 'esm'), exports: 'auto' }))
        .output;
    if (fullOutput) {
        return chunks[0];
    }
    return chunks[0].code;
}
exports.bundleRuleset = bundleRuleset;
//# sourceMappingURL=index.js.map