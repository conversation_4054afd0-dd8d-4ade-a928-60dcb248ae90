/**
 * Stoplight node types
 */
export declare enum NodeType {
    Article = "article",
    HttpService = "http_service",
    HttpServer = "http_server",
    HttpOperation = "http_operation",
    HttpCallback = "http_callback",
    HttpWebhook = "http_webhook",
    Model = "model",
    Generic = "generic",
    Unknown = "unknown",
    TableOfContents = "table_of_contents",
    SpectralRuleset = "spectral_ruleset",
    Styleguide = "styleguide",
    Image = "image",
    StoplightResolutions = "stoplight_resolutions",
    StoplightOverride = "stoplight_override"
}
/**
 * Node data formats
 */
export declare enum NodeFormat {
    Json = "json",
    Markdown = "markdown",
    Yaml = "yaml",
    Javascript = "javascript",
    Apng = "apng",
    Avif = "avif",
    Bmp = "bmp",
    Gif = "gif",
    Jpeg = "jpeg",
    Png = "png",
    Svg = "svg",
    Webp = "webp"
}
