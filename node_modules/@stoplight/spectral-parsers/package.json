{"name": "@stoplight/spectral-parsers", "version": "1.0.5", "homepage": "https://github.com/stoplightio/spectral", "bugs": "https://github.com/stoplightio/spectral/issues", "author": "Stoplight <<EMAIL>>", "engines": {"node": "^16.20 || ^18.18 || >= 20.17"}, "license": "Apache-2.0", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["/dist"], "repository": {"type": "git", "url": "https://github.com/stoplightio/spectral.git"}, "dependencies": {"@stoplight/json": "~3.21.0", "@stoplight/types": "^14.1.1", "@stoplight/yaml": "~4.3.0", "tslib": "^2.8.1"}}