'use strict';
const schema_1 = require("../schema");
var schema = new schema_1.Schema({
    include: [
        require('./core')
    ],
    implicit: [
        require('../type/timestamp'),
        require('../type/merge')
    ],
    explicit: [
        require('../type/binary'),
        require('../type/omap'),
        require('../type/pairs'),
        require('../type/set')
    ]
});
module.exports = schema;
//# sourceMappingURL=default_safe.js.map