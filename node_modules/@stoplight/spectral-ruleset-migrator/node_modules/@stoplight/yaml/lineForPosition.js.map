{"version": 3, "file": "lineForPosition.js", "sourceRoot": "", "sources": ["../src/lineForPosition.ts"], "names": [], "mappings": ";;AAKa,QAAA,eAAe,GAAG,CAAC,GAAW,EAAE,KAAe,EAAE,QAAgB,CAAC,EAAE,GAAY,EAAU,EAAE;IAEvG,IAAI,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE;QACrD,OAAO,CAAC,CAAC;KACV;IAGD,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;QAC9B,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;KACpB;IAGD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;IACrD,IAAI,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;QAC9C,OAAO,MAAM,GAAG,CAAC,CAAC;KACnB;IAGD,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAE9D,IAAI,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;QAC7B,OAAO,MAAM,CAAC;KACf;IAED,IAAI,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,WAAW,EAAE;QAC9C,IAAI,GAAG,KAAK,WAAW,EAAE;YACvB,OAAO,MAAM,GAAG,CAAC,CAAC;SACnB;QAED,OAAO,MAAM,GAAG,CAAC,CAAC;KACnB;IAGD,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,EAAE;QACvB,OAAO,uBAAe,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;KACrD;SAAM;QAEL,OAAO,uBAAe,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;KACvD;AACH,CAAC,CAAC"}