{"name": "@stoplight/yaml", "version": "4.2.3", "description": "Useful functions when working with YAML.", "keywords": ["yaml", "yaml parser", "yaml.parse", "parser", "sourcemap"], "main": "index.js", "sideEffects": false, "files": ["**/*"], "author": "Stoplight <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/stoplightio/yaml"}, "license": "Apache-2.0", "engines": {"node": ">=10.8"}, "dependencies": {"@stoplight/ordered-object-literal": "^1.0.1", "@stoplight/types": "^13.0.0", "@stoplight/yaml-ast-parser": "0.0.48", "tslib": "^2.2.0"}, "typings": "index.d.ts"}