{"version": 3, "file": "getJsonPathForPosition.js", "sourceRoot": "", "sources": ["../src/getJsonPathForPosition.ts"], "names": [], "mappings": ";;AACA,mDAAgD;AAChD,mCAA2D;AAC3D,mCAAmC;AAEtB,QAAA,sBAAsB,GAAsD,CACvF,EAAE,GAAG,EAAE,OAAO,EAAE,EAChB,EAAE,IAAI,EAAE,SAAS,EAAE,EACnB,EAAE;IACF,IAAI,IAAI,IAAI,OAAO,CAAC,MAAM,IAAI,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;QACxD,OAAO;KACR;IAED,MAAM,WAAW,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAE3D,MAAM,IAAI,GAAG,iBAAiB,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,GAAG,SAAS,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACzG,IAAI,CAAC,gBAAQ,CAAC,IAAI,CAAC;QAAE,OAAO;IAE5B,MAAM,IAAI,GAAG,6BAAa,CAAC,IAAI,CAAC,CAAC;IACjC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO;IAC9B,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAc;IAC3B,QAAQ,IAAI,CAAC,IAAI,EAAE;QACjB,KAAK,YAAI,CAAC,GAAG;YACX,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC9B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACnC,IAAI,gBAAQ,CAAC,OAAO,CAAC,EAAE;wBACrB,MAAM,OAAO,CAAC;qBACf;iBACF;aACF;YAED,MAAM;QACR,KAAK,YAAI,CAAC,OAAO;YACf,IAAI,gBAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBACtB,MAAM,IAAI,CAAC,GAAG,CAAC;aAChB;YAED,IAAI,gBAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACxB,MAAM,IAAI,CAAC,KAAK,CAAC;aAClB;YAED,MAAM;QACR,KAAK,YAAI,CAAC,GAAG;YACX,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC3B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;oBAC7B,IAAI,gBAAQ,CAAC,IAAI,CAAC,EAAE;wBAClB,MAAM,IAAI,CAAC;qBACZ;iBACF;aACF;YAED,MAAM;QACR,KAAK,YAAI,CAAC,MAAM;YACd,MAAM,IAAI,CAAC;YACX,MAAM;KACT;AACH,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAc,EAAE,IAAY,EAAE,OAAiB;IAC1E,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1C,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEhC,QAAQ,IAAI,CAAC,IAAI,EAAE;QACjB,KAAK,YAAI,CAAC,OAAO;YACf,OAAO,IAAI,CAAC,GAAG,CAAC;QAClB,KAAK,YAAI,CAAC,GAAG;YACX,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC9B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACnC,IAAI,OAAO,CAAC,aAAa,GAAG,WAAW,IAAI,OAAO,CAAC,aAAa,IAAI,SAAS,EAAE;wBAC7E,OAAO,mBAAmB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;qBACpD;iBACF;aACF;YAED,MAAM;QACR,KAAK,YAAI,CAAC,GAAG;YACX,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC3B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;oBAC7B,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,GAAG,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,SAAS,EAAE;wBACxF,OAAO,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;qBACjD;iBACF;aACF;YAED,MAAM;KACT;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,iBAAiB,CAAC,SAAmB,EAAE,MAAc,EAAE,IAAY,EAAE,OAAiB;IAC7F,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE;QAClC,IAAI,IAAI,CAAC,aAAa,IAAI,MAAM,IAAI,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YAC9D,OAAO,IAAI,CAAC,IAAI,KAAK,YAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;SAC1F;KACF;IACD,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAE3C,OAAO,SAAS,CAAC;KAClB;IAGD,IAAI,SAAS,CAAC,aAAa,GAAG,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,MAAM,IAAI,SAAS,CAAC,WAAW,EAAE;QAClF,IAAI,SAAS,CAAC,IAAI,KAAK,YAAI,CAAC,OAAO,EAAE;YACnC,OAAO,mBAAmB,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;SACtD;QAED,IAAI,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,GAAG,CAAC,WAAW,GAAG,MAAM,EAAE;YACzD,OAAO,mBAAmB,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;SAC5D;KACF;IAED,OAAO,SAAS,CAAC;AACnB,CAAC"}