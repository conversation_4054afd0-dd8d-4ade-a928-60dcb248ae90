{"version": 3, "file": "getLocationForJsonPath.js", "sourceRoot": "", "sources": ["../src/getLocationForJsonPath.ts"], "names": [], "mappings": ";;AAEA,uDAAoD;AACpD,mCAAwE;AACxE,mCAAmC;AAEtB,QAAA,sBAAsB,GAAsD,CACvF,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,EAC1B,IAAI,EACJ,OAAO,GAAG,KAAK,EACf,EAAE;IACF,MAAM,IAAI,GAAG,cAAc,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,KAAK,SAAS,IAAI,QAAQ,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC,CAAC;IACtH,IAAI,IAAI,KAAK,KAAK,CAAC;QAAE,OAAO;IAE5B,OAAO,MAAM,CAAC,OAAO,EAAE;QACrB,KAAK,EAAE,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,GAAG,EAAE,cAAc,CAAC,IAAI,CAAC;KAC1B,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAAS,gBAAgB,CAAC,IAAc,EAAE,MAAc;IACtD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,YAAI,CAAC,OAAO,EAAE;QAEpD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;YAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;SAChC;QAED,IAAI,IAAI,CAAC,IAAI,KAAK,YAAI,CAAC,MAAM,EAAE;YAC7B,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC;SACxC;KACF;IAED,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE;QAC7D,OAAO,CAAC,CAAC;KACV;IAED,OAAO,IAAI,CAAC,aAAa,CAAC;AAC5B,CAAC;AAED,SAAS,cAAc,CAAC,IAAc;IACpC,QAAQ,IAAI,CAAC,IAAI,EAAE;QACjB,KAAK,YAAI,CAAC,GAAG;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;YACvB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtB,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACzC,IAAI,QAAQ,KAAK,IAAI,EAAE;oBACrB,OAAO,cAAc,CAAC,QAAQ,CAAC,CAAC;iBACjC;aACF;YAED,MAAM;QACR,KAAK,YAAI,CAAC,OAAO;YACf,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;gBACvB,OAAO,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACnC;YAED,MAAM;QACR,KAAK,YAAI,CAAC,GAAG;YACX,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gBACrD,OAAO,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;aAChE;YACD,MAAM;QACR,KAAK,YAAI,CAAC,MAAM;YAEd,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,YAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;gBAC3F,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;aAChC;YAED,MAAM;KACT;IAED,OAAO,IAAI,CAAC,WAAW,CAAC;AAC1B,CAAC;AAED,SAAS,cAAc,CACrB,IAAc,EACd,IAAc,EACd,EAAE,OAAO,EAAE,SAAS,EAA4C;IAEhE,QAAQ,EAAE,KAAK,MAAM,OAAO,IAAI,IAAI,EAAE;QACpC,IAAI,CAAC,gBAAQ,CAAC,IAAI,CAAC,EAAE;YACnB,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;SAChC;QAED,QAAQ,IAAI,CAAC,IAAI,EAAE;YACjB,KAAK,YAAI,CAAC,GAAG;gBACX,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;gBAGvD,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;oBAC7C,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACzB,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,OAAO,EAAE;wBAC9B,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;4BACvB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC;yBACjB;6BAAM;4BACL,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;yBACnB;wBACD,SAAS,QAAQ,CAAC;qBACnB;iBACF;gBAED,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YACjC,KAAK,YAAI,CAAC,GAAG;gBACX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC1C,IAAI,CAAC,KAAK,MAAM,CAAC,OAAO,CAAC,EAAE;wBACzB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBAC3B,IAAI,IAAI,KAAK,IAAI,EAAE;4BACjB,MAAM;yBACP;wBAED,IAAI,GAAG,IAAI,CAAC;wBACZ,SAAS,QAAQ,CAAC;qBACnB;iBACF;gBAED,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YACjC;gBACE,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;SAClC;KACF;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,WAAW,CAAC,QAAuB,EAAE,SAAkB;IAC9D,IAAI,CAAC,SAAS;QAAE,OAAO,QAAQ,CAAC;IAEhC,OAAO,QAAQ,CAAC,MAAM,CAAgB,CAAC,cAAc,EAAE,OAAO,EAAE,EAAE;QAChE,IAAI,gBAAQ,CAAC,OAAO,CAAC,EAAE;YACrB,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,SAAgC,EAAE;gBACrD,cAAc,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;aACxD;iBAAM;gBACL,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAC9B;SACF;QAED,OAAO,cAAc,CAAC;IACxB,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAED,SAAS,eAAe,CAAC,IAA+B;IACtD,IAAI,CAAC,gBAAQ,CAAC,IAAI,CAAC;QAAE,OAAO,EAAE,CAAC;IAE/B,QAAQ,IAAI,CAAC,IAAI,EAAE;QACjB,KAAK,YAAI,CAAC,GAAG;YACX,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAgB,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;gBAC3D,KAAK,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;gBACrC,OAAO,KAAK,CAAC;YACf,CAAC,EAAE,EAAE,CAAC,CAAC;QACT,KAAK,YAAI,CAAC,GAAG;YACX,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB,KAAK,YAAI,CAAC,UAAU;YAClB,OAAO,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrC;YACE,OAAO,EAAE,CAAC;KACb;AACH,CAAC;AAED,MAAM,MAAM,GAAG,CAAC,OAAiB,EAAE,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,EAAa,EAAE;IACtE,MAAM,SAAS,GAAG,iCAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAClD,MAAM,OAAO,GAAG,iCAAe,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAE9C,OAAO;QACL,KAAK,EAAE;YACL,KAAK,EAAE;gBACL,IAAI,EAAE,SAAS;gBACf,SAAS,EAAE,KAAK,GAAG,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;aAClE;YACD,GAAG,EAAE;gBACH,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,GAAG,GAAG,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;aAC5D;SACF;KACF,CAAC;AACJ,CAAC,CAAC"}