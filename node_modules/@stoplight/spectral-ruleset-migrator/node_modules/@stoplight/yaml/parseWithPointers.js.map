{"version": 3, "file": "parseWithPointers.js", "sourceRoot": "", "sources": ["../src/parseWithPointers.ts"], "names": [], "mappings": ";;AAAA,8EAAkF;AAClF,4CAA4G;AAC5G,gEAOoC;AACpC,mDAAgD;AAEhD,2DAAwD;AACxD,uDAAoD;AACpD,mCAA+G;AAC/G,mCAAmC;AAEtB,QAAA,iBAAiB,GAAG,CAAI,KAAa,EAAE,OAAuB,EAAmC,EAAE;IAC9G,MAAM,OAAO,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;IACtC,MAAM,GAAG,GAAG,sBAAO,CAAC,KAAK,oBACpB,OAAO,IACV,mBAAmB,EAAE,IAAI,IACb,CAAC;IAEf,MAAM,MAAM,GAAoC;QAC9C,GAAG;QACH,OAAO;QACP,IAAI,EAAE,SAAS;QACf,WAAW,EAAE,EAAE;QACf,QAAQ,EAAE,OAAO;KAClB,CAAC;IAEF,IAAI,CAAC,GAAG;QAAE,OAAO,MAAM,CAAC;IAExB,MAAM,CAAC,IAAI,GAAG,eAAO,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,WAAW,CAAM,CAAC;IAEtE,IAAI,GAAG,CAAC,MAAM,EAAE;QACd,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;KAClE;IAED,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;QACjC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KAC5F;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;QACpC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;KAC9B;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEW,QAAA,OAAO,GAAG,CACrB,IAAqB,EACrB,OAAgC,EAChC,OAAiB,EACjB,WAA0B,EACjB,EAAE;IACX,IAAI,IAAI,EAAE;QACR,QAAQ,IAAI,CAAC,IAAI,EAAE;YACjB,KAAK,YAAI,CAAC,GAAG,CAAC,CAAC;gBACb,MAAM,gBAAgB,GAAG,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,gBAAgB,KAAK,IAAI,CAAC;gBACjF,MAAM,SAAS,GAAG,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;gBAEvD,MAAM,QAAQ,GAAa,EAAE,CAAC;gBAC9B,MAAM,eAAe,GAAG,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,SAAS,KAAK,IAAI,CAAC;gBACzE,MAAM,QAAQ,GAAG,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC;gBAC9D,MAAM,gBAAgB,GAAG,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,mBAAmB,KAAK,KAAK,CAAC;gBAErF,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACnC,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,CAAC;wBAAE,SAAS;oBAE3E,MAAM,GAAG,GAAG,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;oBAEhD,IAAI,CAAC,QAAQ,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,eAAe,IAAI,GAAG,SAAgC,CAAC,EAAE;wBAC/F,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;4BAC1B,IAAI,QAAQ,EAAE;gCACZ,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;6BAC3D;4BAED,IAAI,gBAAgB,EAAE;gCACpB,WAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC,CAAC;6BAC9E;yBACF;6BAAM;4BACL,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;yBACpB;qBACF;oBAGD,IAAI,eAAe,IAAI,GAAG,SAAgC,EAAE;wBAC1D,MAAM,OAAO,GAAG,eAAe,CAAC,eAAO,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,EAAE,gBAAgB,CAAC,CAAC;wBAEzG,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;qBACnC;yBAAM;wBACL,SAAS,CAAC,GAAG,CAAC,GAAG,eAAO,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;wBAEvE,IAAI,gBAAgB,EAAE;4BACpB,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;yBACzB;qBACF;iBACF;gBAED,OAAO,SAAS,CAAC;aAClB;YACD,KAAK,YAAI,CAAC,GAAG;gBACX,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,eAAO,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;YAC9E,KAAK,YAAI,CAAC,MAAM,CAAC,CAAC;gBAChB,MAAM,MAAM,GAAG,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC;gBAC7D,MAAM,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;gBACnC,OAAO,CAAC,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;aACrE;YACD,KAAK,YAAI,CAAC,UAAU,CAAC,CAAC;gBACpB,IAAI,gBAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBACxB,IAAI,CAAC,KAAK,GAAG,qCAAiB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAE,CAAC;iBACpE;gBAED,OAAO,eAAO,CAAC,IAAI,CAAC,KAAM,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;aAC5D;YACD;gBACE,OAAO,IAAI,CAAC;SACf;KACF;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,SAAS,cAAc,CAAC,IAAgB;IACtC,QAAQ,qCAAmB,CAAC,IAAI,CAAC,EAAE;QACjC,KAAK,kBAAU,CAAC,IAAI;YAClB,OAAO,IAAI,CAAC;QACd,KAAK,kBAAU,CAAC,MAAM;YACpB,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5B,KAAK,kBAAU,CAAC,IAAI;YAClB,OAAO,kCAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,KAAK,kBAAU,CAAC,GAAG;YACjB,OAAO,qCAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzC,KAAK,kBAAU,CAAC,KAAK;YACnB,OAAO,gCAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACrC;AACH,CAAC;AAGD,MAAM,cAAc,GAAG,CAAC,KAAa,EAAE,EAAE;IACvC,MAAM,OAAO,GAAa,EAAE,CAAC;IAE7B,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC5B,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YACrB,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SACrB;KACF;IAED,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAEpB,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAEF,SAAS,aAAa,CAAC,OAAiB,EAAE,IAAY;IACpD,IAAI,IAAI,KAAK,CAAC,EAAE;QACd,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KACpC;IAED,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5D,CAAC;AAED,MAAM,eAAe,GAAG,CAAC,MAAuB,EAAE,OAAiB,EAAiB,EAAE;IACpF,MAAM,WAAW,GAAkB,EAAE,CAAC;IACtC,IAAI,sBAAsB,GAAG,CAAC,CAAC,CAAC;IAChC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;QAC1B,MAAM,UAAU,GAAgB;YAC9B,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,MAAM;YACrB,QAAQ,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,0BAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,0BAAkB,CAAC,KAAK;YACjF,KAAK,EAAE;gBACL,KAAK,EAAE;oBACL,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI;oBACrB,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM;iBAC7B;gBACD,GAAG,EAAE;oBACH,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI;oBACrB,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM;iBAC9F;aACF;SACF,CAAC;QAEF,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,KAAK,8CAA8C,CAAC;QACrF,IAAI,YAAY,EAAE;YAChB,sBAAsB,GAAG,sBAAsB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC;SACrF;aAAM,IAAI,sBAAsB,KAAK,CAAC,CAAC,EAAE;YACvC,WAAW,CAAC,sBAAsB,CAAC,CAAC,KAA6C,CAAC,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC;YAC9G,WAAW,CAAC,sBAAsB,CAAC,CAAC,OAAO,GAAG,8CAA8C,CAAC;YAC7F,WAAW,CAAC,MAAM,GAAG,sBAAsB,GAAG,CAAC,CAAC;YAChD,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;YACvB,sBAAsB,GAAG,CAAC,CAAC,CAAC;SAC7B;QAED,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7B,CAAC,EAAE,CAAC;KACL;IAED,OAAO,WAAW,CAAC;AACrB,CAAC,CAAC;AAEF,MAAM,eAAe,GAAG,CAAC,KAAc,EAAE,gBAAyB,EAAiB,EAAE;IACnF,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QAExB,MAAM,OAAO,GAAG,KAAK,CAAC,WAAW,CAC/B,gBAAgB;YACd,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;gBACf,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE/B,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBAE5B,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;oBACzC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC7B;gBAED,OAAO,MAAM,CAAC;YAChB,CAAC;YACH,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,EACjD,kBAAkB,CAAC,gBAAgB,CAAC,CACrC,CAAC;QAEF,OAAO,OAAO,CAAC;KAChB;IAED,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC5E,CAAC,CAAC;AAEF,SAAS,kBAAkB,CAAC,gBAAyB;IACnD,OAAO,gBAAgB,CAAC,CAAC,CAAC,gCAAmB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACzD,CAAC;AAED,SAAS,SAAS,CAAC,SAA8B,EAAE,GAAW;IAC5D,IAAI,CAAC,CAAC,GAAG,IAAI,SAAS,CAAC;QAAE,OAAO;IAChC,MAAM,KAAK,GAAG,iCAAQ,CAAC,SAAS,CAAE,CAAC;IACnC,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACjC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;KACxB;AACH,CAAC;AAED,SAAS,UAAU,CAAC,SAA8B,EAAE,GAAW;IAC7D,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;IAC1B,iCAAQ,CAAC,SAAS,CAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACpC,CAAC;AAED,SAAS,OAAO,CAAC,SAA8B,EAAE,GAAW;IAC1D,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;IAC1B,iCAAQ,CAAC,SAAS,CAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACjC,CAAC;AAED,SAAS,kBAAkB,CACzB,OAAoB,EACpB,OAAiB,EACjB,WAA0B,EAC1B,QAAiB;IAEjB,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,KAAK,YAAI,CAAC,MAAM,EAAE;QACpC,IAAI,CAAC,QAAQ,EAAE;YACb,WAAW,CAAC,IAAI,CACd,kCAAkC,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,EAAE,qCAAqC,EAAE,QAAQ,CAAC,CAC1G,CAAC;SACH;QAGD,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,QAAQ,EAAE;QACb,MAAM,IAAI,GAAG,OAAO,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAChD,IAAI,IAAI,KAAK,QAAQ,EAAE;YACrB,WAAW,CAAC,IAAI,CACd,kCAAkC,CAChC,OAAO,CAAC,GAAG,EACX,OAAO,EACP,mDAAmD,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,EACrG,QAAQ,CACT,CACF,CAAC;SACH;KACF;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,kCAAkC,CACzC,IAAc,EACd,OAAiB,EACjB,OAAe,EACf,QAAiB;IAEjB,MAAM,SAAS,GAAG,mBAAmB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC9D,SAAS,CAAC,IAAI,GAAG,uBAAuB,CAAC;IACzC,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,0BAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,0BAAkB,CAAC,OAAO,CAAC;IACrF,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAc,EAAE,OAAiB,EAAE,OAAe;IAC7E,MAAM,SAAS,GAAG,iCAAe,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IAC/D,MAAM,OAAO,GAAG,iCAAe,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IAE3D,OAAO;QACL,IAAI,EAAE,eAAe;QACrB,OAAO;QACP,QAAQ,EAAE,0BAAkB,CAAC,KAAK;QAClC,IAAI,EAAE,6BAAa,CAAC,IAAI,CAAC;QACzB,KAAK,EAAE;YACL,KAAK,EAAE;gBACL,IAAI,EAAE,SAAS;gBACf,SAAS,EAAE,SAAS,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;aAC9F;YACD,GAAG,EAAE;gBACH,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;aACtF;SACF;KACF,CAAC;AACJ,CAAC"}