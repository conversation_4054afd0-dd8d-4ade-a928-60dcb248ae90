{"version": 3, "file": "dereferenceAnchor.js", "sourceRoot": "", "sources": ["../src/dereferenceAnchor.ts"], "names": [], "mappings": ";;AAAA,mCAA2E;AAC3E,mCAAmC;AAEtB,QAAA,iBAAiB,GAAG,CAAC,IAAqB,EAAE,QAAgB,EAAmB,EAAE;IAC5F,IAAI,CAAC,gBAAQ,CAAC,IAAI,CAAC;QAAE,OAAO,IAAI,CAAC;IACjC,IAAI,IAAI,CAAC,IAAI,KAAK,YAAI,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,KAAK,QAAQ;QAAE,OAAO,IAAI,CAAC;IAErF,QAAQ,IAAI,CAAC,IAAI,EAAE;QACjB,KAAK,YAAI,CAAC,GAAG;YACX,yBACK,IAAI,IACP,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,yBAAiB,CAAC,OAAO,EAAE,QAAQ,CAAgB,CAAC,IAC3F;QACJ,KAAK,YAAI,CAAC,GAAG;YACX,yBACK,IAAI,IACP,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,yBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAE,CAAC,IACjE;QACJ,KAAK,YAAI,CAAC,OAAO;YACf,yBAAY,IAAI,IAAE,KAAK,EAAE,yBAAiB,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAG;QACrE,KAAK,YAAI,CAAC,MAAM;YACd,OAAO,IAAI,CAAC;QACd,KAAK,YAAI,CAAC,UAAU;YAClB,IAAI,gBAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,0BAA0B,CAAC,IAAI,CAAC,EAAE;gBAC5D,OAAO,IAAI,CAAC;aACb;YAED,OAAO,IAAI,CAAC;QACd;YACE,OAAO,IAAI,CAAC;KACf;AACH,CAAC,CAAC;AAEF,MAAM,0BAA0B,GAAG,CAAC,SAA8B,EAAE,EAAE;IACpE,MAAM,EAAE,gBAAgB,EAAE,GAAG,SAAS,CAAC;IACvC,IAAI,IAAI,GAAyB,SAAS,CAAC;IAE3C,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE;QAC3B,IAAI,UAAU,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,gBAAgB,EAAE;YAC5D,OAAO,IAAI,CAAC;SACb;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC"}