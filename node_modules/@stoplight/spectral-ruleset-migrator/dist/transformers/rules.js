"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = void 0;
const tslib_1 = require("tslib");
const ast_types_1 = require("ast-types");
const functions = (0, tslib_1.__importStar)(require("@stoplight/spectral-functions"));
const ordered_object_literal_1 = (0, tslib_1.__importStar)(require("@stoplight/ordered-object-literal"));
const types_1 = require("@stoplight/types");
const validation_1 = require("../validation");
const KNOWN_FUNCTIONS = Object.keys(functions);
const REPLACEMENTS = {
    'operation-2xx-response': 'operation-default-response',
    'oas3-unused-components-schema': 'oas3-unused-components',
    'oas2-valid-parameter-example': 'oas2-valid-schema-example',
    'oas2-valid-response-schema-example': 'oas2-valid-schema-example',
    'oas2-valid-definition-example': 'oas2-valid-schema-example',
    'oas2-valid-response-example': 'oas2-valid-media-example',
    'oas3-valid-oas-parameter-example': 'oas3-valid-media-example',
    'oas3-valid-oas-content-example': 'oas3-valid-media-example',
    'oas3-valid-oas-header-example': 'oas3-valid-media-example',
    'oas3-valid-header-schema-example': 'oas3-valid-schema-example',
    'oas3-valid-parameter-schema-example': 'oas3-valid-schema-example',
};
const SEVERITY_MAP = {
    error: types_1.DiagnosticSeverity.Error,
    warn: types_1.DiagnosticSeverity.Warning,
    info: types_1.DiagnosticSeverity.Information,
    hint: types_1.DiagnosticSeverity.Hint,
    off: -1,
};
function max(left, right) {
    const lSeverity = getDiagnosticSeverity(left);
    const rSeverity = getDiagnosticSeverity(right);
    if (rSeverity === -1) {
        return left;
    }
    return lSeverity > rSeverity ? right : left;
}
function getDiagnosticSeverity(severity) {
    return Number.isNaN(Number(severity)) ? SEVERITY_MAP[severity] : Number(severity);
}
const transformer = function (hooks) {
    hooks.add([
        /^$/,
        (_ruleset) => {
            var _a;
            const ruleset = _ruleset;
            if (ruleset.rules === void 0)
                return;
            const { rules } = ruleset;
            ruleset.rules = (0, ordered_object_literal_1.default)(rules);
            const order = Object.keys(rules);
            for (const [i, key] of order.entries()) {
                if (!(key in REPLACEMENTS))
                    continue;
                if (typeof rules[key] === 'object')
                    continue;
                const newName = REPLACEMENTS[key];
                if (newName in rules) {
                    rules[newName] = max(String(rules[key]), String(rules[newName]));
                }
                else {
                    (_a = rules[newName]) !== null && _a !== void 0 ? _a : (rules[newName] = rules[key]);
                }
                order[i] = newName;
                delete rules[key];
            }
            (0, ordered_object_literal_1.setOrder)(rules, [...new Set([...order])]);
        },
    ]);
    hooks.add([
        /^(?:\/overrides\/\d+)?\/rules\/[^/]+\/then\/(?:\d+\/)?function$/,
        (value, ctx) => {
            (0, validation_1.assertString)(value);
            if (KNOWN_FUNCTIONS.includes(value)) {
                return ctx.tree.addImport(value, '@stoplight/spectral-functions');
            }
            const alias = ctx.tree.scope.load(`function-${value}`);
            return alias !== void 0 ? ast_types_1.builders.identifier(alias) : ast_types_1.builders.unaryExpression('void', ast_types_1.builders.literal(0));
        },
    ]);
};
exports.default = transformer;
//# sourceMappingURL=rules.js.map