import { ValidationOptions } from '../ValidationOptions';
import { CountryCode } from 'libphonenumber-js/max';
export declare const IS_PHONE_NUMBER = "isPhoneNumber";
/**
 * Checks if the string is a valid phone number. To successfully validate any phone number the text must include
 * the intl. calling code, if the calling code wont be provided then the region must be set.
 *
 * @param value the potential phone number string to test
 * @param region 2 characters uppercase country code (e.g. DE, US, CH) for country specific validation.
 * If text doesn't start with the international calling code (e.g. +41), then you must set this parameter.
 */
export declare function isPhoneNumber(value: string, region?: CountryCode): boolean;
/**
 * Checks if the string is a valid phone number. To successfully validate any phone number the text must include
 * the intl. calling code, if the calling code wont be provided then the region must be set.
 *
 * @param region 2 characters uppercase country code (e.g. DE, US, CH) for country specific validation.
 * If text doesn't start with the international calling code (e.g. +41), then you must set this parameter.
 */
export declare function IsPhoneNumber(region?: CountryCode, validationOptions?: ValidationOptions): PropertyDecorator;
