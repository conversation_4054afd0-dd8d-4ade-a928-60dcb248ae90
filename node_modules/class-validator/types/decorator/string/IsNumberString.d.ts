import { ValidationOptions } from '../ValidationOptions';
import * as ValidatorJS from 'validator';
export declare const IS_NUMBER_STRING = "isNumberString";
/**
 * Checks if the string is numeric.
 * If given value is not a string, then it returns false.
 */
export declare function isNumberString(value: unknown, options?: ValidatorJS.IsNumericOptions): boolean;
/**
 * Checks if the string is numeric.
 * If given value is not a string, then it returns false.
 */
export declare function IsNumberString(options?: ValidatorJS.IsNumericOptions, validationOptions?: ValidationOptions): PropertyDecorator;
