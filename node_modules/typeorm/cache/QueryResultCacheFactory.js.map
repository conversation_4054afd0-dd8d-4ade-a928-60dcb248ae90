{"version": 3, "sources": ["../../src/cache/QueryResultCacheFactory.ts"], "names": [], "mappings": ";;;AAAA,mEAA+D;AAC/D,6DAAyD;AAGzD,wDAAoD;AAEpD;;GAEG;AACH,MAAa,uBAAuB;IAChC,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAsB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAG,CAAC;IAEhD,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,MAAM;QACF,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK;YAC9B,MAAM,IAAI,2BAAY,CAClB,uKAAuK,CAC1K,CAAA;QAEL,MAAM,KAAK,GAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAA;QAEhD,IAAI,KAAK,CAAC,QAAQ,IAAI,OAAO,KAAK,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YACzD,OAAO,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAC1C,CAAC;QAED,IACI,KAAK,CAAC,IAAI,KAAK,OAAO;YACtB,KAAK,CAAC,IAAI,KAAK,SAAS;YACxB,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAClC,CAAC;YACC,OAAO,IAAI,6CAAqB,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,CAAA;QACjE,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,uCAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAClD,CAAC;IACL,CAAC;CACJ;AApCD,0DAoCC", "file": "QueryResultCacheFactory.js", "sourcesContent": ["import { RedisQueryResultCache } from \"./RedisQueryResultCache\"\nimport { DbQueryResultCache } from \"./DbQueryResultCache\"\nimport { QueryResultCache } from \"./QueryResultCache\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { TypeORMError } from \"../error/TypeORMError\"\n\n/**\n * Caches query result into Redis database.\n */\nexport class QueryResultCacheFactory {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(protected connection: DataSource) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a new query result cache based on connection options.\n     */\n    create(): QueryResultCache {\n        if (!this.connection.options.cache)\n            throw new TypeORMError(\n                `To use cache you need to enable it in connection options by setting cache: true or providing some caching options. Example: { host: ..., username: ..., cache: true }`,\n            )\n\n        const cache: any = this.connection.options.cache\n\n        if (cache.provider && typeof cache.provider === \"function\") {\n            return cache.provider(this.connection)\n        }\n\n        if (\n            cache.type === \"redis\" ||\n            cache.type === \"ioredis\" ||\n            cache.type === \"ioredis/cluster\"\n        ) {\n            return new RedisQueryResultCache(this.connection, cache.type)\n        } else {\n            return new DbQueryResultCache(this.connection)\n        }\n    }\n}\n"], "sourceRoot": ".."}