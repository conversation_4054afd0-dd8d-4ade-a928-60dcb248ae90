{"version": 3, "sources": ["../../src/cache/DbQueryResultCache.ts"], "names": [], "mappings": ";;;AAEA,uEAAmE;AAEnE,yDAAqD;AAGrD,+BAAmC;AAEnC;;GAEG;AACH,MAAa,kBAAkB;IAW3B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAsB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;QACxC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAc,CAAA;QACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAA;QAChD,MAAM,YAAY,GACd,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,KAAK,QAAQ;YAC7C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK;YAC/B,CAAC,CAAC,EAAE,CAAA;QACZ,MAAM,cAAc,GAAG,YAAY,CAAC,SAAS,IAAI,oBAAoB,CAAA;QAErE,IAAI,CAAC,wBAAwB,GAAG,QAAQ,CAAA;QACxC,IAAI,CAAC,sBAAsB,GAAG,MAAM,CAAA;QACpC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAC9D,cAAc,EACd,MAAM,EACN,QAAQ,CACX,CAAA;IACL,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,OAAO,KAAmB,CAAC;IAEjC;;OAEG;IACH,KAAK,CAAC,UAAU,KAAmB,CAAC;IAEpC;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,WAAyB;QACvC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAA;QACrC,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC,QAAQ,CACzC,IAAI,CAAC,qBAAqB,CAC7B,CAAA,CAAC,0CAA0C;QAC5C,IAAI,UAAU;YAAE,OAAM;QAEtB,MAAM,WAAW,CAAC,WAAW,CACzB,IAAI,aAAK,CAAC;YACN,QAAQ,EAAE,IAAI,CAAC,wBAAwB;YACvC,MAAM,EAAE,IAAI,CAAC,sBAAsB;YACnC,IAAI,EAAE,IAAI,CAAC,qBAAqB;YAChC,OAAO,EAAE;gBACL;oBACI,IAAI,EAAE,IAAI;oBACV,SAAS,EAAE,IAAI;oBACf,UAAU,EAAE,KAAK;oBACjB,IAAI,EAAE,MAAM,CAAC,aAAa,CAAC;wBACvB,IAAI,EAAE,MAAM,CAAC,eAAe,CAAC,OAAO;qBACvC,CAAC;oBACF,kBAAkB,EACd,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS;wBAC7B,CAAC,CAAC,MAAM;wBACR,CAAC,CAAC,WAAW;oBACrB,WAAW,EAAE,IAAI;iBACpB;gBACD;oBACI,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,MAAM,CAAC,aAAa,CAAC;wBACvB,IAAI,EAAE,MAAM,CAAC,eAAe,CAAC,eAAe;qBAC/C,CAAC;oBACF,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,MAAM,CAAC,aAAa,CAAC;wBACvB,IAAI,EAAE,MAAM,CAAC,eAAe,CAAC,SAAS;qBACzC,CAAC;oBACF,SAAS,EAAE,KAAK;oBAChB,UAAU,EAAE,KAAK;iBACpB;gBACD;oBACI,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,MAAM,CAAC,aAAa,CAAC;wBACvB,IAAI,EAAE,MAAM,CAAC,eAAe,CAAC,aAAa;qBAC7C,CAAC;oBACF,SAAS,EAAE,KAAK;oBAChB,UAAU,EAAE,KAAK;iBACpB;gBACD;oBACI,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,MAAM,CAAC,aAAa,CAAC;wBACvB,IAAI,EAAE,MAAM,CAAC,eAAe,CAAC,UAAU;qBAC1C,CAAC;oBACF,SAAS,EAAE,KAAK;oBAChB,UAAU,EAAE,KAAK;iBACpB;gBACD;oBACI,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,MAAM,CAAC,aAAa,CAAC;wBACvB,IAAI,EAAE,MAAM,CAAC,eAAe,CAAC,WAAW;qBAC3C,CAAC;oBACF,UAAU,EAAE,KAAK;iBACpB;aACJ;SACJ,CAAC,CACL,CAAA;IACL,CAAC;IAED;;;;OAIG;IACH,YAAY,CACR,OAAgC,EAChC,WAAyB;QAEzB,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC9C,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU;aACrB,kBAAkB,CAAC,WAAW,CAAC;aAC/B,MAAM,EAAE;aACR,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAA;QAE9C,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACrB,OAAO,EAAE;iBACJ,KAAK,CACF,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,CAC9B,YAAY,CACf,gBAAgB,CACpB;iBACA,aAAa,CAAC;gBACX,UAAU,EACN,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO;oBAC3C,CAAC,CAAC,IAAI,+BAAc,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC;oBACpD,CAAC,CAAC,OAAO,CAAC,UAAU;aAC/B,CAAC;iBACD,KAAK,CAAC,KAAK,CAAC,CAAC,mEAAmE;iBAChF,SAAS,EAAE,CAAA;QACpB,CAAC;aAAM,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACnD,OAAO,EAAE;qBACJ,KAAK,CACF,oBAAoB,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,CAC/C,OAAO,CACV,eAAe,EAChB,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAC3B;qBACA,KAAK,CAAC,KAAK,CAAC,CAAC,mEAAmE;qBAChF,SAAS,EAAE,CAAA;YACpB,CAAC;YAED,OAAO,EAAE;iBACJ,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;iBAC7D,aAAa,CAAC;gBACX,KAAK,EACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO;oBAC3C,CAAC,CAAC,IAAI,+BAAc,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC;oBAC/C,CAAC,CAAC,OAAO,CAAC,KAAK;aAC1B,CAAC;iBACD,KAAK,CAAC,KAAK,CAAC,CAAC,mEAAmE;iBAChF,SAAS,EAAE,CAAA;QACpB,CAAC;QAED,OAAO,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;IACrC,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,UAAmC;QACzC,MAAM,QAAQ,GACV,OAAO,UAAU,CAAC,QAAQ,KAAK,QAAQ;YACnC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;YAC/B,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAA;QAC7B,OAAO,CACH,CAAC,OAAO,UAAU,CAAC,IAAI,KAAK,QAAQ;YAChC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAW,CAAC;YAClC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAE;YACnB,QAAQ;YACZ,IAAI,CAAC,GAAG,EAAE,CACb,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,OAAgC,EAChC,UAA+C,EAC/C,WAAyB;QAEzB,MAAM,uBAAuB,GACzB,WAAW,KAAK,SAAS;YACzB,WAAW,EAAE,kBAAkB,EAAE,KAAK,OAAO,CAAA;QAEjD,IAAI,WAAW,KAAK,SAAS,IAAI,uBAAuB,EAAE,CAAC;YACvD,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAA;QAC7D,CAAC;QAED,IAAI,cAAc,GAAkB,OAAO,CAAA;QAC3C,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClD,iHAAiH;YACjH,cAAc,GAAG;gBACb,UAAU,EAAE,IAAI,+BAAc,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC;gBAC9D,IAAI,EAAE,IAAI,+BAAc,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;gBAChD,QAAQ,EAAE,IAAI,+BAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC;gBACrD,KAAK,EAAE,IAAI,+BAAc,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC;gBACpD,MAAM,EAAE,IAAI,+BAAc,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC;aACzD,CAAA;QACL,CAAC;QAED,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;YACtC,uBAAuB;YACvB,MAAM,EAAE,GAAG,WAAW,CAAC,OAAO;iBACzB,kBAAkB,EAAE;iBACpB,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC;iBAClC,GAAG,CAAC,cAAc,CAAC,CAAA;YAExB,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,eAAe,EAAE;gBAChD,SAAS,EAAE,cAAc,CAAC,UAAU;aACvC,CAAC,CAAA;YACF,MAAM,EAAE,CAAC,OAAO,EAAE,CAAA;QACtB,CAAC;aAAM,IAAI,UAAU,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;YACxC,uBAAuB;YACvB,MAAM,EAAE,GAAG,WAAW,CAAC,OAAO;iBACzB,kBAAkB,EAAE;iBACpB,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC;iBAClC,GAAG,CAAC,cAAc,CAAC,CAAA;YAExB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACnD,EAAE,CAAC,KAAK,CAAC,2CAA2C,EAAE;oBAClD,SAAS,EAAE,cAAc,CAAC,KAAK;iBAClC,CAAC,CAAA;YACN,CAAC;iBAAM,CAAC;gBACJ,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,EAAE;oBAC3C,SAAS,EAAE,cAAc,CAAC,KAAK;iBAClC,CAAC,CAAA;YACN,CAAC;YAED,MAAM,EAAE,CAAC,OAAO,EAAE,CAAA;QACtB,CAAC;aAAM,CAAC;YACJ,kDAAkD;YAClD,IACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS;gBACjD,CAAC,cAAc,CAAC,EAAE,EACpB,CAAC;gBACC,cAAc,CAAC,EAAE,GAAG,IAAA,SAAM,GAAE,CAAA;YAChC,CAAC;YAED,mBAAmB;YACnB,MAAM,WAAW,CAAC,OAAO;iBACpB,kBAAkB,EAAE;iBACpB,MAAM,EAAE;iBACR,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC;iBAChC,MAAM,CAAC,cAAc,CAAC;iBACtB,OAAO,EAAE,CAAA;QAClB,CAAC;QAED,IAAI,uBAAuB,EAAE,CAAC;YAC1B,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QAC/B,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CAAC,WAAwB;QAChC,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,UAAU,CAC9C,IAAI,CAAC,qBAAqB,CAC7B,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACR,WAAqB,EACrB,WAAyB;QAEzB,MAAM,YAAY,GAAgB,WAAW,IAAI,IAAI,CAAC,cAAc,EAAE,CAAA;QACtE,MAAM,OAAO,CAAC,GAAG,CACb,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;YAC3B,MAAM,EAAE,GAAG,YAAY,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAA;YACpD,OAAO,EAAE;iBACJ,MAAM,EAAE;iBACR,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC;iBAChC,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,gBAAgB,EAAE;gBAC/C,UAAU;aACb,CAAC;iBACD,OAAO,EAAE,CAAA;QAClB,CAAC,CAAC,CACL,CAAA;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,MAAM,YAAY,CAAC,OAAO,EAAE,CAAA;QAChC,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,cAAc,CAAC,WAAyB;QAC9C,IAAI,WAAW;YAAE,OAAO,WAAW,CAAA;QAEnC,OAAO,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAA;IAC9C,CAAC;CACJ;AAlUD,gDAkUC", "file": "DbQueryResultCache.js", "sourcesContent": ["import { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { MssqlParameter } from \"../driver/sqlserver/MssqlParameter\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { Table } from \"../schema-builder/table/Table\"\nimport { QueryResultCache } from \"./QueryResultCache\"\nimport { QueryResultCacheOptions } from \"./QueryResultCacheOptions\"\nimport { v4 as uuidv4 } from \"uuid\"\n\n/**\n * Caches query result into current database, into separate table called \"query-result-cache\".\n */\nexport class DbQueryResultCache implements QueryResultCache {\n    // -------------------------------------------------------------------------\n    // Private properties\n    // -------------------------------------------------------------------------\n\n    private queryResultCacheTable: string\n\n    private queryResultCacheDatabase?: string\n\n    private queryResultCacheSchema?: string\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(protected connection: DataSource) {\n        const { schema } = this.connection.driver.options as any\n        const database = this.connection.driver.database\n        const cacheOptions =\n            typeof this.connection.options.cache === \"object\"\n                ? this.connection.options.cache\n                : {}\n        const cacheTableName = cacheOptions.tableName || \"query-result-cache\"\n\n        this.queryResultCacheDatabase = database\n        this.queryResultCacheSchema = schema\n        this.queryResultCacheTable = this.connection.driver.buildTableName(\n            cacheTableName,\n            schema,\n            database,\n        )\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a connection with given cache provider.\n     */\n    async connect(): Promise<void> {}\n\n    /**\n     * Disconnects with given cache provider.\n     */\n    async disconnect(): Promise<void> {}\n\n    /**\n     * Creates table for storing cache if it does not exist yet.\n     */\n    async synchronize(queryRunner?: QueryRunner): Promise<void> {\n        queryRunner = this.getQueryRunner(queryRunner)\n        const driver = this.connection.driver\n        const tableExist = await queryRunner.hasTable(\n            this.queryResultCacheTable,\n        ) // todo: table name should be configurable\n        if (tableExist) return\n\n        await queryRunner.createTable(\n            new Table({\n                database: this.queryResultCacheDatabase,\n                schema: this.queryResultCacheSchema,\n                name: this.queryResultCacheTable,\n                columns: [\n                    {\n                        name: \"id\",\n                        isPrimary: true,\n                        isNullable: false,\n                        type: driver.normalizeType({\n                            type: driver.mappedDataTypes.cacheId,\n                        }),\n                        generationStrategy:\n                            driver.options.type === \"spanner\"\n                                ? \"uuid\"\n                                : \"increment\",\n                        isGenerated: true,\n                    },\n                    {\n                        name: \"identifier\",\n                        type: driver.normalizeType({\n                            type: driver.mappedDataTypes.cacheIdentifier,\n                        }),\n                        isNullable: true,\n                    },\n                    {\n                        name: \"time\",\n                        type: driver.normalizeType({\n                            type: driver.mappedDataTypes.cacheTime,\n                        }),\n                        isPrimary: false,\n                        isNullable: false,\n                    },\n                    {\n                        name: \"duration\",\n                        type: driver.normalizeType({\n                            type: driver.mappedDataTypes.cacheDuration,\n                        }),\n                        isPrimary: false,\n                        isNullable: false,\n                    },\n                    {\n                        name: \"query\",\n                        type: driver.normalizeType({\n                            type: driver.mappedDataTypes.cacheQuery,\n                        }),\n                        isPrimary: false,\n                        isNullable: false,\n                    },\n                    {\n                        name: \"result\",\n                        type: driver.normalizeType({\n                            type: driver.mappedDataTypes.cacheResult,\n                        }),\n                        isNullable: false,\n                    },\n                ],\n            }),\n        )\n    }\n\n    /**\n     * Get data from cache.\n     * Returns cache result if found.\n     * Returns undefined if result is not cached.\n     */\n    getFromCache(\n        options: QueryResultCacheOptions,\n        queryRunner?: QueryRunner,\n    ): Promise<QueryResultCacheOptions | undefined> {\n        queryRunner = this.getQueryRunner(queryRunner)\n        const qb = this.connection\n            .createQueryBuilder(queryRunner)\n            .select()\n            .from(this.queryResultCacheTable, \"cache\")\n\n        if (options.identifier) {\n            return qb\n                .where(\n                    `${qb.escape(\"cache\")}.${qb.escape(\n                        \"identifier\",\n                    )} = :identifier`,\n                )\n                .setParameters({\n                    identifier:\n                        this.connection.driver.options.type === \"mssql\"\n                            ? new MssqlParameter(options.identifier, \"nvarchar\")\n                            : options.identifier,\n                })\n                .cache(false) // disable cache to avoid infinite loops when cache is alwaysEnable\n                .getRawOne()\n        } else if (options.query) {\n            if (this.connection.driver.options.type === \"oracle\") {\n                return qb\n                    .where(\n                        `dbms_lob.compare(${qb.escape(\"cache\")}.${qb.escape(\n                            \"query\",\n                        )}, :query) = 0`,\n                        { query: options.query },\n                    )\n                    .cache(false) // disable cache to avoid infinite loops when cache is alwaysEnable\n                    .getRawOne()\n            }\n\n            return qb\n                .where(`${qb.escape(\"cache\")}.${qb.escape(\"query\")} = :query`)\n                .setParameters({\n                    query:\n                        this.connection.driver.options.type === \"mssql\"\n                            ? new MssqlParameter(options.query, \"nvarchar\")\n                            : options.query,\n                })\n                .cache(false) // disable cache to avoid infinite loops when cache is alwaysEnable\n                .getRawOne()\n        }\n\n        return Promise.resolve(undefined)\n    }\n\n    /**\n     * Checks if cache is expired or not.\n     */\n    isExpired(savedCache: QueryResultCacheOptions): boolean {\n        const duration =\n            typeof savedCache.duration === \"string\"\n                ? parseInt(savedCache.duration)\n                : savedCache.duration\n        return (\n            (typeof savedCache.time === \"string\"\n                ? parseInt(savedCache.time as any)\n                : savedCache.time)! +\n                duration <\n            Date.now()\n        )\n    }\n\n    /**\n     * Stores given query result in the cache.\n     */\n    async storeInCache(\n        options: QueryResultCacheOptions,\n        savedCache: QueryResultCacheOptions | undefined,\n        queryRunner?: QueryRunner,\n    ): Promise<void> {\n        const shouldCreateQueryRunner =\n            queryRunner === undefined ||\n            queryRunner?.getReplicationMode() === \"slave\"\n\n        if (queryRunner === undefined || shouldCreateQueryRunner) {\n            queryRunner = this.connection.createQueryRunner(\"master\")\n        }\n\n        let insertedValues: ObjectLiteral = options\n        if (this.connection.driver.options.type === \"mssql\") {\n            // todo: bad abstraction, re-implement this part, probably better if we create an entity metadata for cache table\n            insertedValues = {\n                identifier: new MssqlParameter(options.identifier, \"nvarchar\"),\n                time: new MssqlParameter(options.time, \"bigint\"),\n                duration: new MssqlParameter(options.duration, \"int\"),\n                query: new MssqlParameter(options.query, \"nvarchar\"),\n                result: new MssqlParameter(options.result, \"nvarchar\"),\n            }\n        }\n\n        if (savedCache && savedCache.identifier) {\n            // if exist then update\n            const qb = queryRunner.manager\n                .createQueryBuilder()\n                .update(this.queryResultCacheTable)\n                .set(insertedValues)\n\n            qb.where(`${qb.escape(\"identifier\")} = :condition`, {\n                condition: insertedValues.identifier,\n            })\n            await qb.execute()\n        } else if (savedCache && savedCache.query) {\n            // if exist then update\n            const qb = queryRunner.manager\n                .createQueryBuilder()\n                .update(this.queryResultCacheTable)\n                .set(insertedValues)\n\n            if (this.connection.driver.options.type === \"oracle\") {\n                qb.where(`dbms_lob.compare(\"query\", :condition) = 0`, {\n                    condition: insertedValues.query,\n                })\n            } else {\n                qb.where(`${qb.escape(\"query\")} = :condition`, {\n                    condition: insertedValues.query,\n                })\n            }\n\n            await qb.execute()\n        } else {\n            // Spanner does not support auto-generated columns\n            if (\n                this.connection.driver.options.type === \"spanner\" &&\n                !insertedValues.id\n            ) {\n                insertedValues.id = uuidv4()\n            }\n\n            // otherwise insert\n            await queryRunner.manager\n                .createQueryBuilder()\n                .insert()\n                .into(this.queryResultCacheTable)\n                .values(insertedValues)\n                .execute()\n        }\n\n        if (shouldCreateQueryRunner) {\n            await queryRunner.release()\n        }\n    }\n\n    /**\n     * Clears everything stored in the cache.\n     */\n    async clear(queryRunner: QueryRunner): Promise<void> {\n        return this.getQueryRunner(queryRunner).clearTable(\n            this.queryResultCacheTable,\n        )\n    }\n\n    /**\n     * Removes all cached results by given identifiers from cache.\n     */\n    async remove(\n        identifiers: string[],\n        queryRunner?: QueryRunner,\n    ): Promise<void> {\n        const _queryRunner: QueryRunner = queryRunner || this.getQueryRunner()\n        await Promise.all(\n            identifiers.map((identifier) => {\n                const qb = _queryRunner.manager.createQueryBuilder()\n                return qb\n                    .delete()\n                    .from(this.queryResultCacheTable)\n                    .where(`${qb.escape(\"identifier\")} = :identifier`, {\n                        identifier,\n                    })\n                    .execute()\n            }),\n        )\n\n        if (!queryRunner) {\n            await _queryRunner.release()\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets a query runner to work with.\n     */\n    protected getQueryRunner(queryRunner?: QueryRunner): QueryRunner {\n        if (queryRunner) return queryRunner\n\n        return this.connection.createQueryRunner()\n    }\n}\n"], "sourceRoot": ".."}