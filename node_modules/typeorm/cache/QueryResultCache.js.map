{"version": 3, "sources": ["../../src/cache/QueryResultCache.ts"], "names": [], "mappings": "", "file": "QueryResultCache.js", "sourcesContent": ["import { QueryResultCacheOptions } from \"./QueryResultCacheOptions\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\n\n/**\n * Implementations of this interface provide different strategies to cache query builder results.\n */\nexport interface QueryResultCache {\n    /**\n     * Creates a connection with given cache provider.\n     */\n    connect(): Promise<void>\n\n    /**\n     * Closes a connection with given cache provider.\n     */\n    disconnect(): Promise<void>\n\n    /**\n     * Perform operations during schema synchronization.\n     */\n    synchronize(queryRunner?: QueryRunner): Promise<void>\n\n    /**\n     * Get data from cache.\n     * Returns cache result if found.\n     * Returns undefined if result is not cached.\n     */\n    getFromCache(\n        options: QueryResultCacheOptions,\n        queryRunner?: QueryRunner,\n    ): Promise<QueryResultCacheOptions | undefined>\n\n    /**\n     * Stores given query result in the cache.\n     */\n    storeInCache(\n        options: QueryResultCacheOptions,\n        savedCache: QueryResultCacheOptions | undefined,\n        queryRunner?: QueryRunner,\n    ): Promise<void>\n\n    /**\n     * Checks if cache is expired or not.\n     */\n    isExpired(savedCache: QueryResultCacheOptions): boolean\n\n    /**\n     * Clears everything stored in the cache.\n     */\n    clear(queryRunner?: QueryRunner): Promise<void>\n\n    /**\n     * Removes all cached results by given identifiers from cache.\n     */\n    remove(identifiers: string[], queryRunner?: QueryRunner): Promise<void>\n}\n"], "sourceRoot": ".."}