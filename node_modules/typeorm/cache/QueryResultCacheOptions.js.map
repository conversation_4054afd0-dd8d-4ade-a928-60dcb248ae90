{"version": 3, "sources": ["../../src/cache/QueryResultCacheOptions.ts"], "names": [], "mappings": "", "file": "QueryResultCacheOptions.js", "sourcesContent": ["/**\n * Options passed to QueryResultCache class.\n */\nexport interface QueryResultCacheOptions {\n    /**\n     * Cache identifier set by user.\n     * Can be empty.\n     */\n    identifier?: string\n\n    /**\n     * Time, when cache was created.\n     */\n    time?: number\n\n    /**\n     * Duration in milliseconds during which results will be returned from cache.\n     */\n    duration: number\n\n    /**\n     * Cached query.\n     */\n    query?: string\n\n    /**\n     * Query result that will be cached.\n     */\n    result?: any\n}\n"], "sourceRoot": ".."}