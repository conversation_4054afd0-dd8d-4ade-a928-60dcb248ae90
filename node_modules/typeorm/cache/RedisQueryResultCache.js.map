{"version": 3, "sources": ["../../src/cache/RedisQueryResultCache.ts"], "names": [], "mappings": ";;;AAEA,6DAAyD;AAGzD,wDAAoD;AAEpD;;GAEG;AACH,MAAa,qBAAqB;IAoB9B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACc,UAAsB,EAChC,UAAmD;QADzC,eAAU,GAAV,UAAU,CAAY;QAGhC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IACjC,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,MAAM,YAAY,GAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAA;QACvD,IAAI,IAAI,CAAC,UAAU,KAAK,OAAO,EAAE,CAAC;YAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;gBAClC,GAAG,YAAY,EAAE,OAAO;gBACxB,UAAU,EAAE,IAAI;aACnB,CAAC,CAAA;YACF,IACI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,KAAK,QAAQ;gBACjD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,EAC5C,CAAC;gBACC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAQ,EAAE,EAAE;oBACjC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;gBAC3C,CAAC,CAAC,CAAA;YACN,CAAC;YACD,IAAI,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC3B,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;YAC/B,CAAC;QACL,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACvC,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC;gBACpC,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;oBACvB,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CACxB,YAAY,CAAC,IAAI,EACjB,YAAY,CAAC,OAAO,CACvB,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;gBACnD,CAAC;YACL,CAAC;iBAAM,IAAI,YAAY,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC9C,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;YACtD,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE,CAAA;YAClC,CAAC;QACL,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,KAAK,iBAAiB,EAAE,CAAC;YAC/C,IACI,YAAY;gBACZ,YAAY,CAAC,OAAO;gBACpB,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,EACrC,CAAC;gBACC,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;YAC9D,CAAC;iBAAM,IACH,YAAY;gBACZ,YAAY,CAAC,OAAO;gBACpB,YAAY,CAAC,OAAO,CAAC,YAAY,EACnC,CAAC;gBACC,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAChC,YAAY,CAAC,OAAO,CAAC,YAAY,EACjC,YAAY,CAAC,OAAO,CAAC,OAAO,CAC/B,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,2BAAY,CAClB,qCAAqC,IAAI,CAAC,UAAU,GAAG,CAC1D,CAAA;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAQ,EAAE,MAAW,EAAE,EAAE;gBACvC,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBACzB,EAAE,EAAE,CAAA;gBACJ,IAAI,CAAC,MAAM,GAAG,SAAS,CAAA;YAC3B,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,WAAwB,IAAkB,CAAC;IAE7D;;;;OAIG;IACH,YAAY,CACR,OAAgC,EAChC,WAAyB;QAEzB,OAAO,IAAI,OAAO,CAAsC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YACjE,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,GAAQ,EAAE,MAAW,EAAE,EAAE;oBAC1D,IAAI,GAAG;wBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;oBACzB,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAA;gBAC1B,CAAC,CAAC,CAAA;YACN,CAAC;iBAAM,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;gBACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,GAAQ,EAAE,MAAW,EAAE,EAAE;oBACrD,IAAI,GAAG;wBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;oBACzB,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAA;gBAC1B,CAAC,CAAC,CAAA;YACN,CAAC;iBAAM,CAAC;gBACJ,EAAE,CAAC,SAAS,CAAC,CAAA;YACjB,CAAC;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,UAAmC;QACzC,OAAO,UAAU,CAAC,IAAK,GAAG,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;IAC9D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,OAAgC,EAChC,UAAmC,EACnC,WAAyB;QAEzB,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,GAAG,CACX,OAAO,CAAC,UAAU,EAClB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EACvB,IAAI,EACJ,OAAO,CAAC,QAAQ,EAChB,CAAC,GAAQ,EAAE,MAAW,EAAE,EAAE;oBACtB,IAAI,GAAG;wBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;oBACzB,EAAE,EAAE,CAAA;gBACR,CAAC,CACJ,CAAA;YACL,CAAC;iBAAM,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;gBACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CACX,OAAO,CAAC,KAAK,EACb,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EACvB,IAAI,EACJ,OAAO,CAAC,QAAQ,EAChB,CAAC,GAAQ,EAAE,MAAW,EAAE,EAAE;oBACtB,IAAI,GAAG;wBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;oBACzB,EAAE,EAAE,CAAA;gBACR,CAAC,CACJ,CAAA;YACL,CAAC;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CAAC,WAAyB;QACjC,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAQ,EAAE,MAAW,EAAE,EAAE;gBAC1C,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBACzB,EAAE,EAAE,CAAA;YACR,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACR,WAAqB,EACrB,WAAyB;QAEzB,MAAM,OAAO,CAAC,GAAG,CACb,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;YAC3B,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;QACrC,CAAC,CAAC,CACL,CAAA;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,SAAS,CAAC,GAAW;QAC3B,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAQ,EAAE,MAAW,EAAE,EAAE;gBAC3C,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBACzB,EAAE,EAAE,CAAA;YACR,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,SAAS;QACf,IAAI,CAAC;YACD,IAAI,IAAI,CAAC,UAAU,KAAK,iBAAiB,EAAE,CAAC;gBACxC,OAAO,6BAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACxC,CAAC;iBAAM,CAAC;gBACJ,OAAO,6BAAa,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YAC9C,CAAC;QACL,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,MAAM,IAAI,2BAAY,CAClB,4BAA4B,IAAI,CAAC,UAAU,wCAAwC,IAAI,CAAC,UAAU,WAAW,CAChH,CAAA;QACL,CAAC;IACL,CAAC;CACJ;AAhPD,sDAgPC", "file": "RedisQueryResultCache.js", "sourcesContent": ["import { QueryResultCache } from \"./QueryResultCache\"\nimport { QueryResultCacheOptions } from \"./QueryResultCacheOptions\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { TypeORMError } from \"../error/TypeORMError\"\n\n/**\n * Caches query result into Redis database.\n */\nexport class RedisQueryResultCache implements QueryResultCache {\n    // -------------------------------------------------------------------------\n    // Protected Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Redis module instance loaded dynamically.\n     */\n    protected redis: any\n\n    /**\n     * Connected redis client.\n     */\n    protected client: any\n\n    /**\n     * Type of the Redis Client (redis or ioredis).\n     */\n    protected clientType: \"redis\" | \"ioredis\" | \"ioredis/cluster\"\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        protected connection: DataSource,\n        clientType: \"redis\" | \"ioredis\" | \"ioredis/cluster\",\n    ) {\n        this.clientType = clientType\n        this.redis = this.loadRedis()\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a connection with given cache provider.\n     */\n    async connect(): Promise<void> {\n        const cacheOptions: any = this.connection.options.cache\n        if (this.clientType === \"redis\") {\n            this.client = this.redis.createClient({\n                ...cacheOptions?.options,\n                legacyMode: true,\n            })\n            if (\n                typeof this.connection.options.cache === \"object\" &&\n                this.connection.options.cache.ignoreErrors\n            ) {\n                this.client.on(\"error\", (err: any) => {\n                    this.connection.logger.log(\"warn\", err)\n                })\n            }\n            if (\"connect\" in this.client) {\n                await this.client.connect()\n            }\n        } else if (this.clientType === \"ioredis\") {\n            if (cacheOptions && cacheOptions.port) {\n                if (cacheOptions.options) {\n                    this.client = new this.redis(\n                        cacheOptions.port,\n                        cacheOptions.options,\n                    )\n                } else {\n                    this.client = new this.redis(cacheOptions.port)\n                }\n            } else if (cacheOptions && cacheOptions.options) {\n                this.client = new this.redis(cacheOptions.options)\n            } else {\n                this.client = new this.redis()\n            }\n        } else if (this.clientType === \"ioredis/cluster\") {\n            if (\n                cacheOptions &&\n                cacheOptions.options &&\n                Array.isArray(cacheOptions.options)\n            ) {\n                this.client = new this.redis.Cluster(cacheOptions.options)\n            } else if (\n                cacheOptions &&\n                cacheOptions.options &&\n                cacheOptions.options.startupNodes\n            ) {\n                this.client = new this.redis.Cluster(\n                    cacheOptions.options.startupNodes,\n                    cacheOptions.options.options,\n                )\n            } else {\n                throw new TypeORMError(\n                    `options.startupNodes required for ${this.clientType}.`,\n                )\n            }\n        }\n    }\n\n    /**\n     * Disconnects the connection\n     */\n    async disconnect(): Promise<void> {\n        return new Promise<void>((ok, fail) => {\n            this.client.quit((err: any, result: any) => {\n                if (err) return fail(err)\n                ok()\n                this.client = undefined\n            })\n        })\n    }\n\n    /**\n     * Creates table for storing cache if it does not exist yet.\n     */\n    async synchronize(queryRunner: QueryRunner): Promise<void> {}\n\n    /**\n     * Get data from cache.\n     * Returns cache result if found.\n     * Returns undefined if result is not cached.\n     */\n    getFromCache(\n        options: QueryResultCacheOptions,\n        queryRunner?: QueryRunner,\n    ): Promise<QueryResultCacheOptions | undefined> {\n        return new Promise<QueryResultCacheOptions | undefined>((ok, fail) => {\n            if (options.identifier) {\n                this.client.get(options.identifier, (err: any, result: any) => {\n                    if (err) return fail(err)\n                    ok(JSON.parse(result))\n                })\n            } else if (options.query) {\n                this.client.get(options.query, (err: any, result: any) => {\n                    if (err) return fail(err)\n                    ok(JSON.parse(result))\n                })\n            } else {\n                ok(undefined)\n            }\n        })\n    }\n\n    /**\n     * Checks if cache is expired or not.\n     */\n    isExpired(savedCache: QueryResultCacheOptions): boolean {\n        return savedCache.time! + savedCache.duration < Date.now()\n    }\n\n    /**\n     * Stores given query result in the cache.\n     */\n    async storeInCache(\n        options: QueryResultCacheOptions,\n        savedCache: QueryResultCacheOptions,\n        queryRunner?: QueryRunner,\n    ): Promise<void> {\n        return new Promise<void>((ok, fail) => {\n            if (options.identifier) {\n                this.client.set(\n                    options.identifier,\n                    JSON.stringify(options),\n                    \"PX\",\n                    options.duration,\n                    (err: any, result: any) => {\n                        if (err) return fail(err)\n                        ok()\n                    },\n                )\n            } else if (options.query) {\n                this.client.set(\n                    options.query,\n                    JSON.stringify(options),\n                    \"PX\",\n                    options.duration,\n                    (err: any, result: any) => {\n                        if (err) return fail(err)\n                        ok()\n                    },\n                )\n            }\n        })\n    }\n\n    /**\n     * Clears everything stored in the cache.\n     */\n    async clear(queryRunner?: QueryRunner): Promise<void> {\n        return new Promise<void>((ok, fail) => {\n            this.client.flushdb((err: any, result: any) => {\n                if (err) return fail(err)\n                ok()\n            })\n        })\n    }\n\n    /**\n     * Removes all cached results by given identifiers from cache.\n     */\n    async remove(\n        identifiers: string[],\n        queryRunner?: QueryRunner,\n    ): Promise<void> {\n        await Promise.all(\n            identifiers.map((identifier) => {\n                return this.deleteKey(identifier)\n            }),\n        )\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Removes a single key from redis database.\n     */\n    protected deleteKey(key: string): Promise<void> {\n        return new Promise<void>((ok, fail) => {\n            this.client.del(key, (err: any, result: any) => {\n                if (err) return fail(err)\n                ok()\n            })\n        })\n    }\n\n    /**\n     * Loads redis dependency.\n     */\n    protected loadRedis(): any {\n        try {\n            if (this.clientType === \"ioredis/cluster\") {\n                return PlatformTools.load(\"ioredis\")\n            } else {\n                return PlatformTools.load(this.clientType)\n            }\n        } catch (e) {\n            throw new TypeORMError(\n                `Cannot use cache because ${this.clientType} is not installed. Please run \"npm i ${this.clientType} --save\".`,\n            )\n        }\n    }\n}\n"], "sourceRoot": ".."}