{"version": 3, "sources": ["../../src/cli-ts-node-esm.ts"], "names": [], "mappings": ";;;AACA,iDAAyC;AAEzC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC;IAChE,OAAO,CAAC,OAAO,CAAC,CAAA;KACf,CAAC;IACF,MAAM,YAAY,GAAG,IAAA,yBAAS,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;QACnE,KAAK,EAAE,SAAS;QAChB,GAAG,EAAE;YACD,GAAG,OAAO,CAAC,GAAG;YACd,YAAY,EAAE;gBACV,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;gBAC3B,sBAAsB;gBACtB,eAAe;aAClB;iBACI,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;iBACxB,IAAI,CAAC,GAAG,CAAC;SACjB;QACD,WAAW,EAAE,IAAI;KACpB,CAAC,CAAA;IAEF,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,CAAC,CAAA;AAC1C,CAAC", "file": "cli-ts-node-esm.js", "sourcesContent": ["#!/usr/bin/env node\nimport { spawnSync } from \"child_process\"\n\nif ((process.env[\"NODE_OPTIONS\"] || \"\").includes(\"--loader ts-node\"))\n    require(\"./cli\")\nelse {\n    const childProcess = spawnSync(process.argv[0], process.argv.slice(1), {\n        stdio: \"inherit\",\n        env: {\n            ...process.env,\n            NODE_OPTIONS: [\n                process.env[\"NODE_OPTIONS\"],\n                \"--loader ts-node/esm\",\n                \"--no-warnings\",\n            ]\n                .filter((item) => !!item)\n                .join(\" \"),\n        },\n        windowsHide: true,\n    })\n\n    process.exit(childProcess.status || 0)\n}\n"], "sourceRoot": "."}