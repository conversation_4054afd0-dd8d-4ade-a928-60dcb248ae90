{"version": 3, "sources": ["../../src/entity-manager/EntityManager.ts"], "names": [], "mappings": ";;;AAIA,sEAAkE;AAClE,8GAA0G;AAK1G,kGAA8F;AAC9F,mEAA+D;AAC/D,iEAA6D;AAC7D,yDAAqD;AACrD,uEAAmE;AACnE,sHAAkH;AAClH,gIAA4H;AAC5H,oCAKiB;AACjB,yEAAqE;AAIrE,gFAA4E;AAO5E,qDAAiD;AACjD,wCAAmD;AAEnD,6DAAyD;AAGzD,qDAAiD;AACjD,+CAA2C;AAE3C;;;GAGG;AACH,MAAa,aAAa;IAuCtB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB,EAAE,WAAyB;QA1CpD,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;QAiBpD,4EAA4E;QAC5E,uBAAuB;QACvB,4EAA4E;QAE5E;;;WAGG;QACO,iBAAY,GAAG,IAAI,GAAG,EAAsC,CAAA;QAEtE;;WAEG;QACO,qBAAgB,GAA0B,EAAE,CAAA;QAEtD;;WAEG;QACO,mCAA8B,GACpC,IAAI,qEAAiC,EAAE,CAAA;QAOvC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,WAAW,EAAE,CAAC;YACd,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;YAC9B,uCAAuC;YACvC,yBAAW,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAA;QAC3D,CAAC;IACL,CAAC;IAuBD;;;OAGG;IACH,KAAK,CAAC,WAAW,CACb,2BAEoD,EACpD,qBAAoE;QAEpE,MAAM,SAAS,GACX,OAAO,2BAA2B,KAAK,QAAQ;YAC3C,CAAC,CAAC,2BAA2B;YAC7B,CAAC,CAAC,SAAS,CAAA;QACnB,MAAM,gBAAgB,GAClB,OAAO,2BAA2B,KAAK,UAAU;YAC7C,CAAC,CAAC,2BAA2B;YAC7B,CAAC,CAAC,qBAAqB,CAAA;QAE/B,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpB,MAAM,IAAI,oBAAY,CAClB,0FAA0F,CAC7F,CAAA;QACL,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU;YAC/C,MAAM,IAAI,iFAAuC,EAAE,CAAA;QAEvD,6HAA6H;QAC7H,6GAA6G;QAC7G,MAAM,WAAW,GACb,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAA;QAE3D,IAAI,CAAC;YACD,MAAM,WAAW,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;YAC7C,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;YAC1D,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAA;YACrC,OAAO,MAAM,CAAA;QACjB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC;gBACD,2DAA2D;gBAC3D,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAA;YAC3C,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC1B,MAAM,GAAG,CAAA;QACb,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,IAAI,CAAC,WAAW;gBACjB,yDAAyD;gBACzD,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QACnC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,KAAK,CAAU,KAAa,EAAE,UAAkB;QAClD,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;IACrE,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,GAAG,CACL,OAA6B,EAC7B,GAAG,MAAiB;QAEpB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,IAAA,yBAAW,EAAC;YACtC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;YAC9B,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,MAAM;SACtB,CAAC,CAAA;QAEF,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;IAC9C,CAAC;IAgBD;;OAEG;IACH,kBAAkB,CACd,WAAgD,EAChD,KAAc,EACd,WAAyB;QAEzB,IAAI,KAAK,EAAE,CAAC;YACR,OAAO,IAAI,CAAC,UAAU,CAAC,kBAAkB,CACrC,WAAmC,EACnC,KAAK,EACL,WAAW,IAAI,IAAI,CAAC,WAAW,CAClC,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,CAAC,UAAU,CAAC,kBAAkB,CACpC,WAAuC;gBACpC,WAAW;gBACX,IAAI,CAAC,WAAW,CACvB,CAAA;QACL,CAAC;IACL,CAAC;IAYD;;OAEG;IACH,KAAK,CAAC,cAAuC,EAAE,WAAiB;QAC5D,MAAM,MAAM,GACR,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,CAAA;QACxE,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc,CAAA;QACpE,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QACpD,OAAO,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;IACjC,CAAC;IAYD;;OAEG;IACH,KAAK,CAAC,cAAuC,EAAE,WAAiB;QAC5D,MAAM,MAAM,GACR,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,CAAA;QACxE,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc,CAAA;QACpE,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QACpD,OAAO,QAAQ,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAA;IAC/C,CAAC;IAoBD;;;OAGG;IACH,MAAM,CACF,WAAiC,EACjC,oBAAgD;QAEhD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QAEzD,IAAI,CAAC,oBAAoB;YAAE,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAEnE,IAAI,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC;YACnC,OAAQ,oBAAqC,CAAC,GAAG,CAC7C,CAAC,eAAe,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,eAAe,CAAC,CACjE,CAAA;QAEL,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACzD,IAAI,CAAC,8BAA8B,CAAC,SAAS,CACzC,eAAe,EACf,oBAAoB,EACpB,QAAQ,EACR,IAAI,CACP,CAAA;QACD,OAAO,eAAe,CAAA;IAC1B,CAAC;IAED;;OAEG;IACH,KAAK,CACD,WAAiC,EACjC,eAAuB,EACvB,GAAG,WAAkC;QAErC,sDAAsD;QACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QACzD,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAC3B,IAAI,CAAC,8BAA8B,CAAC,SAAS,CACzC,eAAe,EACf,MAAM,EACN,QAAQ,CACX,CACJ,CAAA;QACD,OAAO,eAAe,CAAA;IAC1B,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,OAAO,CACT,WAAiC,EACjC,UAA+B;QAE/B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QACzD,MAAM,sCAAsC,GACxC,IAAI,+EAAsC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;QACvE,MAAM,iBAAiB,GACnB,MAAM,sCAAsC,CAAC,SAAS,CAClD,UAAU,EACV,QAAQ,CACX,CAAA;QACL,IAAI,iBAAiB;YACjB,OAAO,IAAI,CAAC,KAAK,CACb,WAAkB,EAClB,iBAA2B,EAC3B,UAAU,CACb,CAAA;QAEL,OAAO,SAAS,CAAA;IACpB,CAAC;IAsDD;;OAEG;IACH,IAAI,CACA,cAAgD,EAChD,oBAA8B,EAC9B,YAA0B;QAE1B,6BAA6B;QAC7B,IAAI,MAAM,GACN,SAAS,CAAC,MAAM,GAAG,CAAC;YACpB,CAAC,OAAO,cAAc,KAAK,UAAU;gBACjC,iCAAe,CAAC,cAAc,CAAC,cAAc,CAAC;gBAC9C,OAAO,cAAc,KAAK,QAAQ,CAAC;YACnC,CAAC,CAAE,cAAoC;YACvC,CAAC,CAAC,SAAS,CAAA;QACnB,MAAM,MAAM,GAAY,MAAM;YAC1B,CAAC,CAAE,oBAAgC;YACnC,CAAC,CAAE,cAA0B,CAAA;QACjC,MAAM,OAAO,GAAG,MAAM;YAClB,CAAC,CAAC,YAAY;YACd,CAAC,CAAE,oBAAoC,CAAA;QAE3C,IAAI,iCAAe,CAAC,cAAc,CAAC,MAAM,CAAC;YAAE,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAA;QAExE,2EAA2E;QAC3E,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5C,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAElC,yBAAyB;QACzB,OAAO,IAAI,6CAAqB,CAC5B,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,WAAW,EAChB,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,CACV;aACI,OAAO,EAAE;aACT,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAA;IAC3B,CAAC;IA8BD;;OAEG;IACH,MAAM,CACF,cAA0D,EAC1D,oBAAwC,EACxC,YAA4B;QAE5B,6BAA6B;QAC7B,MAAM,MAAM,GACR,SAAS,CAAC,MAAM,GAAG,CAAC;YACpB,CAAC,OAAO,cAAc,KAAK,UAAU;gBACjC,iCAAe,CAAC,cAAc,CAAC,cAAc,CAAC;gBAC9C,OAAO,cAAc,KAAK,QAAQ,CAAC;YACnC,CAAC,CAAE,cAAoC;YACvC,CAAC,CAAC,SAAS,CAAA;QACnB,MAAM,MAAM,GAAsB,MAAM;YACpC,CAAC,CAAE,oBAA0C;YAC7C,CAAC,CAAE,cAAoC,CAAA;QAC3C,MAAM,OAAO,GAAG,MAAM;YAClB,CAAC,CAAC,YAAY;YACd,CAAC,CAAE,oBAAoC,CAAA;QAE3C,2EAA2E;QAC3E,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5C,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAElC,yBAAyB;QACzB,OAAO,IAAI,6CAAqB,CAC5B,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,WAAW,EAChB,QAAQ,EACR,MAAM,EACN,MAAM,EACN,OAAO,CACV;aACI,OAAO,EAAE;aACT,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAA;IAC3B,CAAC;IAiCD;;OAEG;IACH,UAAU,CACN,cAAgD,EAChD,oBAA8B,EAC9B,YAA0B;QAE1B,6BAA6B;QAC7B,IAAI,MAAM,GACN,SAAS,CAAC,MAAM,GAAG,CAAC;YACpB,CAAC,OAAO,cAAc,KAAK,UAAU;gBACjC,iCAAe,CAAC,cAAc,CAAC,cAAc,CAAC;gBAC9C,OAAO,cAAc,KAAK,QAAQ,CAAC;YACnC,CAAC,CAAE,cAAoC;YACvC,CAAC,CAAC,SAAS,CAAA;QACnB,MAAM,MAAM,GAAY,MAAM;YAC1B,CAAC,CAAE,oBAAgC;YACnC,CAAC,CAAE,cAA0B,CAAA;QACjC,MAAM,OAAO,GAAG,MAAM;YAClB,CAAC,CAAC,YAAY;YACd,CAAC,CAAE,oBAAoC,CAAA;QAE3C,IAAI,iCAAe,CAAC,cAAc,CAAC,MAAM,CAAC;YAAE,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAA;QAExE,2EAA2E;QAC3E,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5C,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAElC,gCAAgC;QAChC,OAAO,IAAI,6CAAqB,CAC5B,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,WAAW,EAChB,aAAa,EACb,MAAM,EACN,MAAM,EACN,OAAO,CACV;aACI,OAAO,EAAE;aACT,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAA;IAC3B,CAAC;IAiCD;;OAEG;IACH,OAAO,CACH,cAAgD,EAChD,oBAA8B,EAC9B,YAA0B;QAE1B,6BAA6B;QAC7B,IAAI,MAAM,GACN,SAAS,CAAC,MAAM,GAAG,CAAC;YACpB,CAAC,OAAO,cAAc,KAAK,UAAU;gBACjC,iCAAe,CAAC,cAAc,CAAC,cAAc,CAAC;gBAC9C,OAAO,cAAc,KAAK,QAAQ,CAAC;YACnC,CAAC,CAAE,cAAoC;YACvC,CAAC,CAAC,SAAS,CAAA;QACnB,MAAM,MAAM,GAAY,MAAM;YAC1B,CAAC,CAAE,oBAAgC;YACnC,CAAC,CAAE,cAA0B,CAAA;QACjC,MAAM,OAAO,GAAG,MAAM;YAClB,CAAC,CAAC,YAAY;YACd,CAAC,CAAE,oBAAoC,CAAA;QAE3C,IAAI,iCAAe,CAAC,cAAc,CAAC,MAAM,CAAC;YAAE,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAA;QAExE,2EAA2E;QAC3E,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5C,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAElC,4BAA4B;QAC5B,OAAO,IAAI,6CAAqB,CAC5B,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,WAAW,EAChB,SAAS,EACT,MAAM,EACN,MAAM,EACN,OAAO,CACV;aACI,OAAO,EAAE;aACT,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAA;IAC3B,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,MAAM,CACR,MAA4B,EAC5B,MAEsC;QAEtC,OAAO,IAAI,CAAC,kBAAkB,EAAE;aAC3B,MAAM,EAAE;aACR,IAAI,CAAC,MAAM,CAAC;aACZ,MAAM,CAAC,MAAM,CAAC;aACd,OAAO,EAAE,CAAA;IAClB,CAAC;IAED,KAAK,CAAC,MAAM,CACR,MAA4B,EAC5B,gBAEsC,EACtC,sBAAwD;QAExD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QAEpD,IAAI,OAA8B,CAAA;QAElC,IAAI,KAAK,CAAC,OAAO,CAAC,sBAAsB,CAAC,EAAE,CAAC;YACxC,OAAO,GAAG;gBACN,aAAa,EAAE,sBAAsB;aACxC,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,OAAO,GAAG,sBAAsB,CAAA;QACpC,CAAC;QAED,IAAI,QAA0C,CAAA;QAE9C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACnC,QAAQ,GAAG,CAAC,gBAAgB,CAAC,CAAA;QACjC,CAAC;aAAM,CAAC;YACJ,QAAQ,GAAG,gBAAgB,CAAA;QAC/B,CAAC;QAED,MAAM,eAAe,GAAG,QAAQ,CAAC,yBAAyB,CACtD,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC;YAChC,CAAC,CAAC,OAAO,CAAC,aAAa;YACvB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAC3C,CAAA;QAED,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAC5C,CAAC,GAAG,EAAE,EAAE,CACJ,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC;YAC9B,QAAQ,CAAC,IAAI,CACT,CAAC,MAAM,EAAE,EAAE,CACP,OAAO,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,WAAW,CACxD,CACR,CAAA;QAED,OAAO,IAAI,CAAC,kBAAkB,EAAE;aAC3B,MAAM,EAAE;aACR,IAAI,CAAC,MAAM,CAAC;aACZ,MAAM,CAAC,QAAQ,CAAC;aAChB,QAAQ,CACL,CAAC,GAAG,eAAe,EAAE,GAAG,gBAAgB,CAAC,CAAC,GAAG,CACzC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,YAAY,CAC5B,EACD,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAC9C;YACI,2BAA2B,EACvB,OAAO,CAAC,2BAA2B;YACvC,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,UAAU,EACN,OAAO,CAAC,UAAU;gBAClB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC;SACrD,CACJ;aACA,OAAO,EAAE,CAAA;IAClB,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CACF,MAA4B,EAC5B,QASS,EACT,aAA6C;QAE7C,gFAAgF;QAChF,IAAI,mBAAQ,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3C,OAAO,OAAO,CAAC,MAAM,CACjB,IAAI,oBAAY,CACZ,0DAA0D,CAC7D,CACJ,CAAA;QACL,CAAC;QAED,IAAI,mBAAQ,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC,kBAAkB,EAAE;iBAC3B,MAAM,CAAC,MAAM,CAAC;iBACd,GAAG,CAAC,aAAa,CAAC;iBAClB,UAAU,CAAC,QAAQ,CAAC;iBACpB,OAAO,EAAE,CAAA;QAClB,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,CAAC,kBAAkB,EAAE;iBAC3B,MAAM,CAAC,MAAM,CAAC;iBACd,GAAG,CAAC,aAAa,CAAC;iBAClB,KAAK,CAAC,QAAQ,CAAC;iBACf,OAAO,EAAE,CAAA;QAClB,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,SAAS,CACL,MAA4B,EAC5B,aAA6C;QAE7C,OAAO,IAAI,CAAC,kBAAkB,EAAE;aAC3B,MAAM,CAAC,MAAM,CAAC;aACd,GAAG,CAAC,aAAa,CAAC;aAClB,OAAO,EAAE,CAAA;IAClB,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CACF,cAAoC,EACpC,QASS;QAET,gFAAgF;QAChF,IAAI,mBAAQ,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3C,OAAO,OAAO,CAAC,MAAM,CACjB,IAAI,oBAAY,CACZ,0DAA0D,CAC7D,CACJ,CAAA;QACL,CAAC;QAED,IAAI,mBAAQ,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC,kBAAkB,EAAE;iBAC3B,MAAM,EAAE;iBACR,IAAI,CAAC,cAAc,CAAC;iBACpB,UAAU,CAAC,QAAQ,CAAC;iBACpB,OAAO,EAAE,CAAA;QAClB,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,CAAC,kBAAkB,EAAE;iBAC3B,MAAM,EAAE;iBACR,IAAI,CAAC,cAAc,CAAC;iBACpB,KAAK,CAAC,QAAQ,CAAC;iBACf,OAAO,EAAE,CAAA;QAClB,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,SAAS,CACL,cAAoC;QAEpC,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,EAAE,CAAA;IAC5E,CAAC;IAED;;;;;;OAMG;IACH,UAAU,CACN,cAAoC,EACpC,QASS;QAET,gFAAgF;QAChF,IAAI,mBAAQ,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3C,OAAO,OAAO,CAAC,MAAM,CACjB,IAAI,oBAAY,CACZ,8DAA8D,CACjE,CACJ,CAAA;QACL,CAAC;QAED,IAAI,mBAAQ,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC,kBAAkB,EAAE;iBAC3B,UAAU,EAAE;iBACZ,IAAI,CAAC,cAAc,CAAC;iBACpB,UAAU,CAAC,QAAQ,CAAC;iBACpB,OAAO,EAAE,CAAA;QAClB,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,CAAC,kBAAkB,EAAE;iBAC3B,UAAU,EAAE;iBACZ,IAAI,CAAC,cAAc,CAAC;iBACpB,KAAK,CAAC,QAAQ,CAAC;iBACf,OAAO,EAAE,CAAA;QAClB,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,OAAO,CACH,cAAoC,EACpC,QASS;QAET,gFAAgF;QAChF,IAAI,mBAAQ,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3C,OAAO,OAAO,CAAC,MAAM,CACjB,IAAI,oBAAY,CACZ,2DAA2D,CAC9D,CACJ,CAAA;QACL,CAAC;QAED,IAAI,mBAAQ,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC,kBAAkB,EAAE;iBAC3B,OAAO,EAAE;iBACT,IAAI,CAAC,cAAc,CAAC;iBACpB,UAAU,CAAC,QAAQ,CAAC;iBACpB,OAAO,EAAE,CAAA;QAClB,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,CAAC,kBAAkB,EAAE;iBAC3B,OAAO,EAAE;iBACT,IAAI,CAAC,cAAc,CAAC;iBACpB,KAAK,CAAC,QAAQ,CAAC;iBACf,OAAO,EAAE,CAAA;QAClB,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CACF,WAAiC,EACjC,OAAiC;QAEjC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QACzD,OAAO,IAAI,CAAC,kBAAkB,CAC1B,WAAW,EACX,mCAAgB,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACjD,QAAQ,CAAC,IAAI,CACpB;aACI,cAAc,CAAC,OAAO,IAAI,EAAE,CAAC;aAC7B,SAAS,EAAE,CAAA;IACpB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CACV,WAAiC,EACjC,KAA4D;QAE5D,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QACzD,OAAO,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC;aACrD,cAAc,CAAC,EAAE,KAAK,EAAE,CAAC;aACzB,SAAS,EAAE,CAAA;IACpB,CAAC;IAED;;;OAGG;IACH,KAAK,CACD,WAAiC,EACjC,OAAiC;QAEjC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QACzD,OAAO,IAAI,CAAC,kBAAkB,CAC1B,WAAW,EACX,mCAAgB,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACjD,QAAQ,CAAC,IAAI,CACpB;aACI,cAAc,CAAC,OAAO,IAAI,EAAE,CAAC;aAC7B,QAAQ,EAAE,CAAA;IACnB,CAAC;IAED;;;OAGG;IACH,OAAO,CACH,WAAiC,EACjC,KAA4D;QAE5D,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QACzD,OAAO,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC;aACrD,cAAc,CAAC,EAAE,KAAK,EAAE,CAAC;aACzB,QAAQ,EAAE,CAAA;IACnB,CAAC;IAED;;OAEG;IACH,GAAG,CACC,WAAiC,EACjC,UAA0C,EAC1C,KAA6D;QAE7D,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;IACvE,CAAC;IAED;;OAEG;IACH,OAAO,CACH,WAAiC,EACjC,UAA0C,EAC1C,KAA6D;QAE7D,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;IACvE,CAAC;IAED;;OAEG;IACH,OAAO,CACH,WAAiC,EACjC,UAA0C,EAC1C,KAA6D;QAE7D,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;IACvE,CAAC;IAED;;OAEG;IACH,OAAO,CACH,WAAiC,EACjC,UAA0C,EAC1C,KAA6D;QAE7D,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;IACvE,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAC1B,WAAiC,EACjC,MAAqC,EACrC,UAA0C,EAC1C,QAA+D,EAAE;QAEjE,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QACzD,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAChC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,KAAK,UAAU,CAC7C,CAAA;QACD,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,IAAI,oBAAY,CAClB,WAAW,UAAU,6BAA6B,QAAQ,CAAC,IAAI,GAAG,CACrE,CAAA;QACL,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC;aACnE,cAAc,CAAC,EAAE,KAAK,EAAE,CAAC;aACzB,MAAM,CACH,GAAG,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CACtC,MAAM,CAAC,YAAY,CACtB,GAAG,EACJ,MAAM,CACT;aACA,SAAS,EAAE,CAAA;QAChB,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAA;IACtE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI,CACN,WAAiC,EACjC,OAAiC;QAEjC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QACzD,OAAO,IAAI,CAAC,kBAAkB,CAC1B,WAAkB,EAClB,mCAAgB,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACjD,QAAQ,CAAC,IAAI,CACpB;aACI,cAAc,CAAC,OAAO,IAAI,EAAE,CAAC;aAC7B,OAAO,EAAE,CAAA;IAClB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACR,WAAiC,EACjC,KAA4D;QAE5D,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QACzD,OAAO,IAAI,CAAC,kBAAkB,CAC1B,WAAkB,EAClB,QAAQ,CAAC,IAAI,CAChB;aACI,cAAc,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;aAChC,OAAO,EAAE,CAAA;IAClB,CAAC;IAED;;;;OAIG;IACH,YAAY,CACR,WAAiC,EACjC,OAAiC;QAEjC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QACzD,OAAO,IAAI,CAAC,kBAAkB,CAC1B,WAAkB,EAClB,mCAAgB,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACjD,QAAQ,CAAC,IAAI,CACpB;aACI,cAAc,CAAC,OAAO,IAAI,EAAE,CAAC;aAC7B,eAAe,EAAE,CAAA;IAC1B,CAAC;IAED;;;;OAIG;IACH,cAAc,CACV,WAAiC,EACjC,KAA4D;QAE5D,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QACzD,OAAO,IAAI,CAAC,kBAAkB,CAC1B,WAAkB,EAClB,QAAQ,CAAC,IAAI,CAChB;aACI,cAAc,CAAC,EAAE,KAAK,EAAE,CAAC;aACzB,eAAe,EAAE,CAAA;IAC1B,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,SAAS,CACX,WAAiC,EACjC,GAAU;QAEV,sFAAsF;QACtF,IAAI,CAAC,GAAG,CAAC,MAAM;YAAE,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;QAE3C,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QACzD,OAAO,IAAI,CAAC,kBAAkB,CAC1B,WAAkB,EAClB,QAAQ,CAAC,IAAI,CAChB;aACI,aAAa,CAAC,GAAG,CAAC;aAClB,OAAO,EAAE,CAAA;IAClB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,OAAO,CACT,WAAiC,EACjC,OAA+B;QAE/B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QAEzD,gCAAgC;QAChC,IAAI,KAAK,GAAW,QAAQ,CAAC,IAAI,CAAA;QACjC,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YAC1B,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAA;QAC9B,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CACX,sEAAsE,CACzE,CAAA;QACL,CAAC;QAED,8CAA8C;QAC9C,OAAO,IAAI,CAAC,kBAAkB,CAAS,WAAW,EAAE,KAAK,CAAC;aACrD,cAAc,CAAC;YACZ,GAAG,OAAO;YACV,IAAI,EAAE,CAAC;SACV,CAAC;aACD,MAAM,EAAE,CAAA;IACjB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,SAAS,CACX,WAAiC,EACjC,KAA4D;QAE5D,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QAEzD,8CAA8C;QAC9C,OAAO,IAAI,CAAC,kBAAkB,CAAS,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC;aAC7D,cAAc,CAAC;YACZ,KAAK;YACL,IAAI,EAAE,CAAC;SACV,CAAC;aACD,MAAM,EAAE,CAAA;IACjB,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,WAAW,CACb,WAAiC,EACjC,EAAqC;QAErC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QAEzD,8CAA8C;QAC9C,OAAO,IAAI,CAAC,kBAAkB,CAAS,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC;aAC7D,cAAc,CAAC;YACZ,IAAI,EAAE,CAAC;SACV,CAAC;aACD,UAAU,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;aAC1C,MAAM,EAAE,CAAA;IACjB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa,CACf,WAAiC,EACjC,OAA+B;QAE/B,OAAO,IAAI,CAAC,OAAO,CAAS,WAAkB,EAAE,OAAO,CAAC,CAAC,IAAI,CACzD,CAAC,KAAK,EAAE,EAAE;YACN,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBACjB,OAAO,OAAO,CAAC,MAAM,CACjB,IAAI,yCAAmB,CAAC,WAAW,EAAE,OAAO,CAAC,CAChD,CAAA;YACL,CAAC;YACD,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QACjC,CAAC,CACJ,CAAA;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,eAAe,CACjB,WAAiC,EACjC,KAA4D;QAE5D,OAAO,IAAI,CAAC,SAAS,CAAS,WAAkB,EAAE,KAAK,CAAC,CAAC,IAAI,CACzD,CAAC,KAAK,EAAE,EAAE;YACN,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBACjB,OAAO,OAAO,CAAC,MAAM,CACjB,IAAI,yCAAmB,CAAC,WAAW,EAAE,KAAK,CAAC,CAC9C,CAAA;YACL,CAAC;YACD,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QACjC,CAAC,CACJ,CAAA;IACL,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,KAAK,CAAS,WAAiC;QACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QACzD,MAAM,WAAW,GACb,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAA;QAC3D,IAAI,CAAC;YACD,OAAO,MAAM,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA,CAAC,oDAAoD;QAChH,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,IAAI,CAAC,WAAW;gBAAE,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QACtD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAAiC,EACjC,UAAe,EACf,YAAoB,EACpB,KAAsB;QAEtB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QACzD,MAAM,MAAM,GAAG,QAAQ,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAA;QAChE,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,oBAAY,CAClB,UAAU,YAAY,qBAAqB,QAAQ,CAAC,UAAU,UAAU,CAC3E,CAAA;QAEL,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACpB,MAAM,IAAI,oBAAY,CAAC,UAAU,KAAK,oBAAoB,CAAC,CAAA;QAE/D,8FAA8F;QAC9F,MAAM,MAAM,GAAmC,YAAY;aACtD,KAAK,CAAC,GAAG,CAAC;aACV,WAAW,CACR,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAU,CAAA,EACzC,GAAG,EAAE,CACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAClD,KAAK;YACL,KAAK,CACZ,CAAA;QAEL,OAAO,IAAI,CAAC,kBAAkB,CAAS,WAAkB,EAAE,QAAQ,CAAC;aAC/D,MAAM,CAAC,WAAW,CAAC;aACnB,GAAG,CAAC,MAAM,CAAC;aACX,KAAK,CAAC,UAAU,CAAC;aACjB,OAAO,EAAE,CAAA;IAClB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAAiC,EACjC,UAAe,EACf,YAAoB,EACpB,KAAsB;QAEtB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;QACzD,MAAM,MAAM,GAAG,QAAQ,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAA;QAChE,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,oBAAY,CAClB,UAAU,YAAY,qBAAqB,QAAQ,CAAC,UAAU,UAAU,CAC3E,CAAA;QAEL,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACpB,MAAM,IAAI,oBAAY,CAAC,UAAU,KAAK,oBAAoB,CAAC,CAAA;QAE/D,8FAA8F;QAC9F,MAAM,MAAM,GAAmC,YAAY;aACtD,KAAK,CAAC,GAAG,CAAC;aACV,WAAW,CACR,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAU,CAAA,EACzC,GAAG,EAAE,CACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAClD,KAAK;YACL,KAAK,CACZ,CAAA;QAEL,OAAO,IAAI,CAAC,kBAAkB,CAAS,WAAkB,EAAE,QAAQ,CAAC;aAC/D,MAAM,CAAC,WAAW,CAAC;aACnB,GAAG,CAAC,MAAM,CAAC;aACX,KAAK,CAAC,UAAU,CAAC;aACjB,OAAO,EAAE,CAAA;IAClB,CAAC;IAED;;;;;OAKG;IACH,aAAa,CACT,MAA4B;QAE5B,kEAAkE;QAClE,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACjD,IAAI,WAAW;YAAE,OAAO,WAAW,CAAA;QAEnC,+EAA+E;QAC/E,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACpD,MAAM,aAAa,GAAG,IAAI,iCAAe,CACrC,MAAM,EACN,IAAI,EACJ,IAAI,CAAC,WAAW,CACnB,CAAA;YACD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC,CAAA;YAC5C,OAAO,aAAa,CAAA;QACxB,CAAC;aAAM,CAAC;YACJ,MAAM,aAAa,GAAG,IAAI,uBAAU,CAChC,MAAM,EACN,IAAI,EACJ,IAAI,CAAC,WAAW,CACnB,CAAA;YACD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC,CAAA;YAC5C,OAAO,aAAa,CAAA;QACxB,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,iBAAiB,CACb,MAA4B;QAE5B,yDAAyD;QACzD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,KAAK,KAAK;YAC5C,MAAM,IAAI,uCAA+B,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;QAErE,kEAAkE;QAClE,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CACzC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,MAAM,KAAK,MAAM,CAC/C,CAAA;QACD,IAAI,UAAU;YAAE,OAAO,UAAU,CAAA;QAEjC,8CAA8C;QAC9C,MAAM,aAAa,GAAG,IAAI,+BAAc,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;QACxE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;QACzC,OAAO,aAAa,CAAA;IACxB,CAAC;IAED;;OAEG;IACH,kBAAkB,CACd,MAA4B;QAE5B,OAAO,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAS,MAAM,CAAC,CAAA;IAC7D,CAAC;IAED;;;;OAIG;IACH,cAAc,CACV,UAAkC;QAElC,MAAM,qBAAqB,GACvB,UAAU,CAAC,WAAgC,CAAA;QAC/C,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,yBAAyB,EAAE,GAChE,UAAU,CAAA;QACd,OAAO,MAAM,CAAC,MAAM,CAChB,IAAI,qBAAqB,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAM,EACvD;YACI,GAAG,yBAAyB;SAC/B,CACJ,CAAA;IACL,CAAC;IAED;;;;OAIG;IACH,mBAAmB,CAAI,gBAA+B;QAClD,MAAM,4BAA4B,GAC9B,IAAA,gCAAsB,GAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;YAC5D,OAAO,CACH,UAAU,CAAC,MAAM;gBACjB,CAAC,OAAO,gBAAgB,KAAK,UAAU;oBACnC,CAAC,CAAC,gBAAgB;oBAClB,CAAC,CAAE,gBAAwB,CAAC,WAAW,CAAC,CAC/C,CAAA;QACL,CAAC,CAAC,CAAA;QACN,IAAI,CAAC,4BAA4B;YAC7B,MAAM,IAAI,qCAA6B,CAAC,gBAAgB,CAAC,CAAA;QAE7D,MAAM,cAAc,GAAG,4BAA4B,CAAC,MAAM;YACtD,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,4BAA4B,CAAC,MAAM,CAAC;YAClE,CAAC,CAAC,SAAS,CAAA;QACf,MAAM,wBAAwB,GAC1B,IAAK,4BAA4B,CAAC,MAAc,CAC5C,IAAI,EACJ,cAAc,CACjB,CAAA;QAEL,4HAA4H;QAC5H,kEAAkE;QAClE,IAAI,wBAAwB,YAAY,uCAAkB,EAAE,CAAC;YACzD,IAAI,CAAE,wBAAgC,CAAC,SAAS,CAAC;gBAC5C,wBAAgC,CAAC,SAAS,CAAC,GAAG,IAAI,CAAA;QAC3D,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,cAAc;gBACf,MAAM,IAAI,oDAA4C,CAClD,gBAAgB,CACnB,CACJ;YAAC,wBAAgC,CAAC,SAAS,CAAC,GAAG,IAAI,CACnD;YAAC,wBAAgC,CAAC,UAAU,CAAC,GAAG,cAAc,CAAA;QACnE,CAAC;QAED,OAAO,wBAAwB,CAAA;IACnC,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,MAAM,IAAI,qEAAiC,EAAE,CAAA;QAEpE,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAA;IACrC,CAAC;CACJ;AA59CD,sCA49CC", "file": "EntityManager.js", "sourcesContent": ["import { DataSource } from \"../data-source/DataSource\"\nimport { FindManyOptions } from \"../find-options/FindManyOptions\"\nimport { EntityTarget } from \"../common/EntityTarget\"\nimport { ObjectType } from \"../common/ObjectType\"\nimport { EntityNotFoundError } from \"../error/EntityNotFoundError\"\nimport { QueryRunnerProviderAlreadyReleasedError } from \"../error/QueryRunnerProviderAlreadyReleasedError\"\nimport { FindOneOptions } from \"../find-options/FindOneOptions\"\nimport { DeepPartial } from \"../common/DeepPartial\"\nimport { RemoveOptions } from \"../repository/RemoveOptions\"\nimport { SaveOptions } from \"../repository/SaveOptions\"\nimport { NoNeedToReleaseEntityManagerError } from \"../error/NoNeedToReleaseEntityManagerError\"\nimport { MongoRepository } from \"../repository/MongoRepository\"\nimport { TreeRepository } from \"../repository/TreeRepository\"\nimport { Repository } from \"../repository/Repository\"\nimport { FindOptionsUtils } from \"../find-options/FindOptionsUtils\"\nimport { PlainObjectToNewEntityTransformer } from \"../query-builder/transformer/PlainObjectToNewEntityTransformer\"\nimport { PlainObjectToDatabaseEntityTransformer } from \"../query-builder/transformer/PlainObjectToDatabaseEntityTransformer\"\nimport {\n    CustomRepositoryCannotInheritRepositoryError,\n    CustomRepositoryNotFoundError,\n    TreeRepositoryNotSupportedError,\n    TypeORMError,\n} from \"../error\"\nimport { AbstractRepository } from \"../repository/AbstractRepository\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { SelectQueryBuilder } from \"../query-builder/SelectQueryBuilder\"\nimport { QueryDeepPartialEntity } from \"../query-builder/QueryPartialEntity\"\nimport { EntityPersistExecutor } from \"../persistence/EntityPersistExecutor\"\nimport { ObjectId } from \"../driver/mongodb/typings\"\nimport { InsertResult } from \"../query-builder/result/InsertResult\"\nimport { UpdateResult } from \"../query-builder/result/UpdateResult\"\nimport { DeleteResult } from \"../query-builder/result/DeleteResult\"\nimport { FindOptionsWhere } from \"../find-options/FindOptionsWhere\"\nimport { IsolationLevel } from \"../driver/types/IsolationLevel\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { getMetadataArgsStorage } from \"../globals\"\nimport { UpsertOptions } from \"../repository/UpsertOptions\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { PickKeysByType } from \"../common/PickKeysByType\"\nimport { buildSqlTag } from \"../util/SqlTagUtils\"\nimport { OrmUtils } from \"../util/OrmUtils\"\n\n/**\n * Entity manager supposed to work with any entity, automatically find its repository and call its methods,\n * whatever entity type are you passing.\n */\nexport class EntityManager {\n    readonly \"@instanceof\" = Symbol.for(\"EntityManager\")\n\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection used by this entity manager.\n     */\n    readonly connection: DataSource\n\n    /**\n     * Custom query runner to be used for operations in this entity manager.\n     * Used only in non-global entity manager.\n     */\n    readonly queryRunner?: QueryRunner\n\n    // -------------------------------------------------------------------------\n    // Protected Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Once created and then reused by repositories.\n     * Created as a future replacement for the #repositories to provide a bit more perf optimization.\n     */\n    protected repositories = new Map<EntityTarget<any>, Repository<any>>()\n\n    /**\n     * Once created and then reused by repositories.\n     */\n    protected treeRepositories: TreeRepository<any>[] = []\n\n    /**\n     * Plain to object transformer used in create and merge operations.\n     */\n    protected plainObjectToEntityTransformer =\n        new PlainObjectToNewEntityTransformer()\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource, queryRunner?: QueryRunner) {\n        this.connection = connection\n        if (queryRunner) {\n            this.queryRunner = queryRunner\n            // dynamic: this.queryRunner = manager;\n            ObjectUtils.assign(this.queryRunner, { manager: this })\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Wraps given function execution (and all operations made there) in a transaction.\n     * All database operations must be executed using provided entity manager.\n     */\n    async transaction<T>(\n        runInTransaction: (entityManager: EntityManager) => Promise<T>,\n    ): Promise<T>\n\n    /**\n     * Wraps given function execution (and all operations made there) in a transaction.\n     * All database operations must be executed using provided entity manager.\n     */\n    async transaction<T>(\n        isolationLevel: IsolationLevel,\n        runInTransaction: (entityManager: EntityManager) => Promise<T>,\n    ): Promise<T>\n\n    /**\n     * Wraps given function execution (and all operations made there) in a transaction.\n     * All database operations must be executed using provided entity manager.\n     */\n    async transaction<T>(\n        isolationOrRunInTransaction:\n            | IsolationLevel\n            | ((entityManager: EntityManager) => Promise<T>),\n        runInTransactionParam?: (entityManager: EntityManager) => Promise<T>,\n    ): Promise<T> {\n        const isolation =\n            typeof isolationOrRunInTransaction === \"string\"\n                ? isolationOrRunInTransaction\n                : undefined\n        const runInTransaction =\n            typeof isolationOrRunInTransaction === \"function\"\n                ? isolationOrRunInTransaction\n                : runInTransactionParam\n\n        if (!runInTransaction) {\n            throw new TypeORMError(\n                `Transaction method requires callback in second parameter if isolation level is supplied.`,\n            )\n        }\n\n        if (this.queryRunner && this.queryRunner.isReleased)\n            throw new QueryRunnerProviderAlreadyReleasedError()\n\n        // if query runner is already defined in this class, it means this entity manager was already created for a single connection\n        // if its not defined we create a new query runner - single connection where we'll execute all our operations\n        const queryRunner =\n            this.queryRunner || this.connection.createQueryRunner()\n\n        try {\n            await queryRunner.startTransaction(isolation)\n            const result = await runInTransaction(queryRunner.manager)\n            await queryRunner.commitTransaction()\n            return result\n        } catch (err) {\n            try {\n                // we throw original error even if rollback thrown an error\n                await queryRunner.rollbackTransaction()\n            } catch (rollbackError) {}\n            throw err\n        } finally {\n            if (!this.queryRunner)\n                // if we used a new query runner provider then release it\n                await queryRunner.release()\n        }\n    }\n\n    /**\n     * Executes raw SQL query and returns raw database results.\n     *\n     * @see [Official docs](https://typeorm.io/entity-manager-api) for examples.\n     */\n    async query<T = any>(query: string, parameters?: any[]): Promise<T> {\n        return this.connection.query(query, parameters, this.queryRunner)\n    }\n\n    /**\n     * Tagged template function that executes raw SQL query and returns raw database results.\n     * Template expressions are automatically transformed into database parameters.\n     * Raw query execution is supported only by relational databases (MongoDB is not supported).\n     * Note: Don't call this as a regular function, it is meant to be used with backticks to tag a template literal.\n     * Example: entityManager.sql`SELECT * FROM table_name WHERE id = ${id}`\n     */\n    async sql<T = any>(\n        strings: TemplateStringsArray,\n        ...values: unknown[]\n    ): Promise<T> {\n        const { query, parameters } = buildSqlTag({\n            driver: this.connection.driver,\n            strings: strings,\n            expressions: values,\n        })\n\n        return await this.query(query, parameters)\n    }\n\n    /**\n     * Creates a new query builder that can be used to build a SQL query.\n     */\n    createQueryBuilder<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        alias: string,\n        queryRunner?: QueryRunner,\n    ): SelectQueryBuilder<Entity>\n\n    /**\n     * Creates a new query builder that can be used to build a SQL query.\n     */\n    createQueryBuilder(queryRunner?: QueryRunner): SelectQueryBuilder<any>\n\n    /**\n     * Creates a new query builder that can be used to build a SQL query.\n     */\n    createQueryBuilder<Entity extends ObjectLiteral>(\n        entityClass?: EntityTarget<Entity> | QueryRunner,\n        alias?: string,\n        queryRunner?: QueryRunner,\n    ): SelectQueryBuilder<Entity> {\n        if (alias) {\n            return this.connection.createQueryBuilder(\n                entityClass as EntityTarget<Entity>,\n                alias,\n                queryRunner || this.queryRunner,\n            )\n        } else {\n            return this.connection.createQueryBuilder(\n                (entityClass as QueryRunner | undefined) ||\n                    queryRunner ||\n                    this.queryRunner,\n            )\n        }\n    }\n\n    /**\n     * Checks if entity has an id.\n     */\n    hasId(entity: any): boolean\n\n    /**\n     * Checks if entity of given schema name has an id.\n     */\n    hasId(target: Function | string, entity: any): boolean\n\n    /**\n     * Checks if entity has an id by its Function type or schema name.\n     */\n    hasId(targetOrEntity: any | Function | string, maybeEntity?: any): boolean {\n        const target =\n            arguments.length === 2 ? targetOrEntity : targetOrEntity.constructor\n        const entity = arguments.length === 2 ? maybeEntity : targetOrEntity\n        const metadata = this.connection.getMetadata(target)\n        return metadata.hasId(entity)\n    }\n\n    /**\n     * Gets entity mixed id.\n     */\n    getId(entity: any): any\n\n    /**\n     * Gets entity mixed id.\n     */\n    getId(target: EntityTarget<any>, entity: any): any\n\n    /**\n     * Gets entity mixed id.\n     */\n    getId(targetOrEntity: any | EntityTarget<any>, maybeEntity?: any): any {\n        const target =\n            arguments.length === 2 ? targetOrEntity : targetOrEntity.constructor\n        const entity = arguments.length === 2 ? maybeEntity : targetOrEntity\n        const metadata = this.connection.getMetadata(target)\n        return metadata.getEntityIdMixedMap(entity)\n    }\n\n    /**\n     * Creates a new entity instance and copies all entity properties from this object into a new entity.\n     * Note that it copies only properties that present in entity schema.\n     */\n    create<Entity, EntityLike extends DeepPartial<Entity>>(\n        entityClass: EntityTarget<Entity>,\n        plainObject?: EntityLike,\n    ): Entity\n\n    /**\n     * Creates a new entities and copies all entity properties from given objects into their new entities.\n     * Note that it copies only properties that present in entity schema.\n     */\n    create<Entity, EntityLike extends DeepPartial<Entity>>(\n        entityClass: EntityTarget<Entity>,\n        plainObjects?: EntityLike[],\n    ): Entity[]\n\n    /**\n     * Creates a new entity instance or instances.\n     * Can copy properties from the given object into new entities.\n     */\n    create<Entity, EntityLike extends DeepPartial<Entity>>(\n        entityClass: EntityTarget<Entity>,\n        plainObjectOrObjects?: EntityLike | EntityLike[],\n    ): Entity | Entity[] {\n        const metadata = this.connection.getMetadata(entityClass)\n\n        if (!plainObjectOrObjects) return metadata.create(this.queryRunner)\n\n        if (Array.isArray(plainObjectOrObjects))\n            return (plainObjectOrObjects as EntityLike[]).map(\n                (plainEntityLike) => this.create(entityClass, plainEntityLike),\n            )\n\n        const mergeIntoEntity = metadata.create(this.queryRunner)\n        this.plainObjectToEntityTransformer.transform(\n            mergeIntoEntity,\n            plainObjectOrObjects,\n            metadata,\n            true,\n        )\n        return mergeIntoEntity\n    }\n\n    /**\n     * Merges two entities into one new entity.\n     */\n    merge<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        mergeIntoEntity: Entity,\n        ...entityLikes: DeepPartial<Entity>[]\n    ): Entity {\n        // todo: throw exception if entity manager is released\n        const metadata = this.connection.getMetadata(entityClass)\n        entityLikes.forEach((object) =>\n            this.plainObjectToEntityTransformer.transform(\n                mergeIntoEntity,\n                object,\n                metadata,\n            ),\n        )\n        return mergeIntoEntity\n    }\n\n    /**\n     * Creates a new entity from the given plain javascript object. If entity already exist in the database, then\n     * it loads it (and everything related to it), replaces all values with the new ones from the given object\n     * and returns this new entity. This new entity is actually a loaded from the db entity with all properties\n     * replaced from the new object.\n     */\n    async preload<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        entityLike: DeepPartial<Entity>,\n    ): Promise<Entity | undefined> {\n        const metadata = this.connection.getMetadata(entityClass)\n        const plainObjectToDatabaseEntityTransformer =\n            new PlainObjectToDatabaseEntityTransformer(this.connection.manager)\n        const transformedEntity =\n            await plainObjectToDatabaseEntityTransformer.transform(\n                entityLike,\n                metadata,\n            )\n        if (transformedEntity)\n            return this.merge(\n                entityClass as any,\n                transformedEntity as Entity,\n                entityLike,\n            )\n\n        return undefined\n    }\n\n    /**\n     * Saves all given entities in the database.\n     * If entities do not exist in the database then inserts, otherwise updates.\n     */\n    save<Entity>(entities: Entity[], options?: SaveOptions): Promise<Entity[]>\n\n    /**\n     * Saves all given entities in the database.\n     * If entities do not exist in the database then inserts, otherwise updates.\n     */\n    save<Entity>(entity: Entity, options?: SaveOptions): Promise<Entity>\n\n    /**\n     * Saves all given entities in the database.\n     * If entities do not exist in the database then inserts, otherwise updates.\n     */\n    save<Entity, T extends DeepPartial<Entity>>(\n        targetOrEntity: EntityTarget<Entity>,\n        entities: T[],\n        options: SaveOptions & { reload: false },\n    ): Promise<T[]>\n\n    /**\n     * Saves all given entities in the database.\n     * If entities do not exist in the database then inserts, otherwise updates.\n     */\n    save<Entity, T extends DeepPartial<Entity>>(\n        targetOrEntity: EntityTarget<Entity>,\n        entities: T[],\n        options?: SaveOptions,\n    ): Promise<(T & Entity)[]>\n\n    /**\n     * Saves a given entity in the database.\n     * If entity does not exist in the database then inserts, otherwise updates.\n     */\n    save<Entity, T extends DeepPartial<Entity>>(\n        targetOrEntity: EntityTarget<Entity>,\n        entity: T,\n        options: SaveOptions & { reload: false },\n    ): Promise<T>\n\n    /**\n     * Saves a given entity in the database.\n     * If entity does not exist in the database then inserts, otherwise updates.\n     */\n    save<Entity, T extends DeepPartial<Entity>>(\n        targetOrEntity: EntityTarget<Entity>,\n        entity: T,\n        options?: SaveOptions,\n    ): Promise<T & Entity>\n\n    /**\n     * Saves a given entity in the database.\n     */\n    save<Entity extends ObjectLiteral, T extends DeepPartial<Entity>>(\n        targetOrEntity: (T | T[]) | EntityTarget<Entity>,\n        maybeEntityOrOptions?: T | T[],\n        maybeOptions?: SaveOptions,\n    ): Promise<T | T[]> {\n        // normalize mixed parameters\n        let target =\n            arguments.length > 1 &&\n            (typeof targetOrEntity === \"function\" ||\n                InstanceChecker.isEntitySchema(targetOrEntity) ||\n                typeof targetOrEntity === \"string\")\n                ? (targetOrEntity as Function | string)\n                : undefined\n        const entity: T | T[] = target\n            ? (maybeEntityOrOptions as T | T[])\n            : (targetOrEntity as T | T[])\n        const options = target\n            ? maybeOptions\n            : (maybeEntityOrOptions as SaveOptions)\n\n        if (InstanceChecker.isEntitySchema(target)) target = target.options.name\n\n        // if user passed empty array of entities then we don't need to do anything\n        if (Array.isArray(entity) && entity.length === 0)\n            return Promise.resolve(entity)\n\n        // execute save operation\n        return new EntityPersistExecutor(\n            this.connection,\n            this.queryRunner,\n            \"save\",\n            target,\n            entity,\n            options,\n        )\n            .execute()\n            .then(() => entity)\n    }\n\n    /**\n     * Removes a given entity from the database.\n     */\n    remove<Entity>(entity: Entity, options?: RemoveOptions): Promise<Entity>\n\n    /**\n     * Removes a given entity from the database.\n     */\n    remove<Entity>(\n        targetOrEntity: EntityTarget<Entity>,\n        entity: Entity,\n        options?: RemoveOptions,\n    ): Promise<Entity>\n\n    /**\n     * Removes a given entity from the database.\n     */\n    remove<Entity>(entity: Entity[], options?: RemoveOptions): Promise<Entity>\n\n    /**\n     * Removes a given entity from the database.\n     */\n    remove<Entity>(\n        targetOrEntity: EntityTarget<Entity>,\n        entity: Entity[],\n        options?: RemoveOptions,\n    ): Promise<Entity[]>\n\n    /**\n     * Removes a given entity from the database.\n     */\n    remove<Entity extends ObjectLiteral>(\n        targetOrEntity: (Entity | Entity[]) | EntityTarget<Entity>,\n        maybeEntityOrOptions?: Entity | Entity[],\n        maybeOptions?: RemoveOptions,\n    ): Promise<Entity | Entity[]> {\n        // normalize mixed parameters\n        const target =\n            arguments.length > 1 &&\n            (typeof targetOrEntity === \"function\" ||\n                InstanceChecker.isEntitySchema(targetOrEntity) ||\n                typeof targetOrEntity === \"string\")\n                ? (targetOrEntity as Function | string)\n                : undefined\n        const entity: Entity | Entity[] = target\n            ? (maybeEntityOrOptions as Entity | Entity[])\n            : (targetOrEntity as Entity | Entity[])\n        const options = target\n            ? maybeOptions\n            : (maybeEntityOrOptions as SaveOptions)\n\n        // if user passed empty array of entities then we don't need to do anything\n        if (Array.isArray(entity) && entity.length === 0)\n            return Promise.resolve(entity)\n\n        // execute save operation\n        return new EntityPersistExecutor(\n            this.connection,\n            this.queryRunner,\n            \"remove\",\n            target,\n            entity,\n            options,\n        )\n            .execute()\n            .then(() => entity)\n    }\n\n    /**\n     * Records the delete date of all given entities.\n     */\n    softRemove<Entity>(\n        entities: Entity[],\n        options?: SaveOptions,\n    ): Promise<Entity[]>\n\n    /**\n     * Records the delete date of a given entity.\n     */\n    softRemove<Entity>(entity: Entity, options?: SaveOptions): Promise<Entity>\n\n    /**\n     * Records the delete date of all given entities.\n     */\n    softRemove<Entity, T extends DeepPartial<Entity>>(\n        targetOrEntity: EntityTarget<Entity>,\n        entities: T[],\n        options?: SaveOptions,\n    ): Promise<T[]>\n\n    /**\n     * Records the delete date of a given entity.\n     */\n    softRemove<Entity, T extends DeepPartial<Entity>>(\n        targetOrEntity: EntityTarget<Entity>,\n        entity: T,\n        options?: SaveOptions,\n    ): Promise<T>\n\n    /**\n     * Records the delete date of one or many given entities.\n     */\n    softRemove<Entity extends ObjectLiteral, T extends DeepPartial<Entity>>(\n        targetOrEntity: (T | T[]) | EntityTarget<Entity>,\n        maybeEntityOrOptions?: T | T[],\n        maybeOptions?: SaveOptions,\n    ): Promise<T | T[]> {\n        // normalize mixed parameters\n        let target =\n            arguments.length > 1 &&\n            (typeof targetOrEntity === \"function\" ||\n                InstanceChecker.isEntitySchema(targetOrEntity) ||\n                typeof targetOrEntity === \"string\")\n                ? (targetOrEntity as Function | string)\n                : undefined\n        const entity: T | T[] = target\n            ? (maybeEntityOrOptions as T | T[])\n            : (targetOrEntity as T | T[])\n        const options = target\n            ? maybeOptions\n            : (maybeEntityOrOptions as SaveOptions)\n\n        if (InstanceChecker.isEntitySchema(target)) target = target.options.name\n\n        // if user passed empty array of entities then we don't need to do anything\n        if (Array.isArray(entity) && entity.length === 0)\n            return Promise.resolve(entity)\n\n        // execute soft-remove operation\n        return new EntityPersistExecutor(\n            this.connection,\n            this.queryRunner,\n            \"soft-remove\",\n            target,\n            entity,\n            options,\n        )\n            .execute()\n            .then(() => entity)\n    }\n\n    /**\n     * Recovers all given entities.\n     */\n    recover<Entity>(\n        entities: Entity[],\n        options?: SaveOptions,\n    ): Promise<Entity[]>\n\n    /**\n     * Recovers a given entity.\n     */\n    recover<Entity>(entity: Entity, options?: SaveOptions): Promise<Entity>\n\n    /**\n     * Recovers all given entities.\n     */\n    recover<Entity, T extends DeepPartial<Entity>>(\n        targetOrEntity: EntityTarget<Entity>,\n        entities: T[],\n        options?: SaveOptions,\n    ): Promise<T[]>\n\n    /**\n     * Recovers a given entity.\n     */\n    recover<Entity, T extends DeepPartial<Entity>>(\n        targetOrEntity: EntityTarget<Entity>,\n        entity: T,\n        options?: SaveOptions,\n    ): Promise<T>\n\n    /**\n     * Recovers one or many given entities.\n     */\n    recover<Entity extends ObjectLiteral, T extends DeepPartial<Entity>>(\n        targetOrEntity: (T | T[]) | EntityTarget<Entity>,\n        maybeEntityOrOptions?: T | T[],\n        maybeOptions?: SaveOptions,\n    ): Promise<T | T[]> {\n        // normalize mixed parameters\n        let target =\n            arguments.length > 1 &&\n            (typeof targetOrEntity === \"function\" ||\n                InstanceChecker.isEntitySchema(targetOrEntity) ||\n                typeof targetOrEntity === \"string\")\n                ? (targetOrEntity as Function | string)\n                : undefined\n        const entity: T | T[] = target\n            ? (maybeEntityOrOptions as T | T[])\n            : (targetOrEntity as T | T[])\n        const options = target\n            ? maybeOptions\n            : (maybeEntityOrOptions as SaveOptions)\n\n        if (InstanceChecker.isEntitySchema(target)) target = target.options.name\n\n        // if user passed empty array of entities then we don't need to do anything\n        if (Array.isArray(entity) && entity.length === 0)\n            return Promise.resolve(entity)\n\n        // execute recover operation\n        return new EntityPersistExecutor(\n            this.connection,\n            this.queryRunner,\n            \"recover\",\n            target,\n            entity,\n            options,\n        )\n            .execute()\n            .then(() => entity)\n    }\n\n    /**\n     * Inserts a given entity into the database.\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient INSERT query.\n     * Does not check if entity exist in the database, so query will fail if duplicate entity is being inserted.\n     * You can execute bulk inserts using this method.\n     */\n    async insert<Entity extends ObjectLiteral>(\n        target: EntityTarget<Entity>,\n        entity:\n            | QueryDeepPartialEntity<Entity>\n            | QueryDeepPartialEntity<Entity>[],\n    ): Promise<InsertResult> {\n        return this.createQueryBuilder()\n            .insert()\n            .into(target)\n            .values(entity)\n            .execute()\n    }\n\n    async upsert<Entity extends ObjectLiteral>(\n        target: EntityTarget<Entity>,\n        entityOrEntities:\n            | QueryDeepPartialEntity<Entity>\n            | QueryDeepPartialEntity<Entity>[],\n        conflictPathsOrOptions: string[] | UpsertOptions<Entity>,\n    ): Promise<InsertResult> {\n        const metadata = this.connection.getMetadata(target)\n\n        let options: UpsertOptions<Entity>\n\n        if (Array.isArray(conflictPathsOrOptions)) {\n            options = {\n                conflictPaths: conflictPathsOrOptions,\n            }\n        } else {\n            options = conflictPathsOrOptions\n        }\n\n        let entities: QueryDeepPartialEntity<Entity>[]\n\n        if (!Array.isArray(entityOrEntities)) {\n            entities = [entityOrEntities]\n        } else {\n            entities = entityOrEntities\n        }\n\n        const conflictColumns = metadata.mapPropertyPathsToColumns(\n            Array.isArray(options.conflictPaths)\n                ? options.conflictPaths\n                : Object.keys(options.conflictPaths),\n        )\n\n        const overwriteColumns = metadata.columns.filter(\n            (col) =>\n                !conflictColumns.includes(col) &&\n                entities.some(\n                    (entity) =>\n                        typeof col.getEntityValue(entity) !== \"undefined\",\n                ),\n        )\n\n        return this.createQueryBuilder()\n            .insert()\n            .into(target)\n            .values(entities)\n            .orUpdate(\n                [...conflictColumns, ...overwriteColumns].map(\n                    (col) => col.databaseName,\n                ),\n                conflictColumns.map((col) => col.databaseName),\n                {\n                    skipUpdateIfNoValuesChanged:\n                        options.skipUpdateIfNoValuesChanged,\n                    indexPredicate: options.indexPredicate,\n                    upsertType:\n                        options.upsertType ||\n                        this.connection.driver.supportedUpsertTypes[0],\n                },\n            )\n            .execute()\n    }\n\n    /**\n     * Updates entity partially. Entity can be found by a given condition(s).\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient UPDATE query.\n     * Does not check if entity exist in the database.\n     * Condition(s) cannot be empty.\n     */\n    update<Entity extends ObjectLiteral>(\n        target: EntityTarget<Entity>,\n        criteria:\n            | string\n            | string[]\n            | number\n            | number[]\n            | Date\n            | Date[]\n            | ObjectId\n            | ObjectId[]\n            | any,\n        partialEntity: QueryDeepPartialEntity<Entity>,\n    ): Promise<UpdateResult> {\n        // if user passed empty criteria or empty list of criterias, then throw an error\n        if (OrmUtils.isCriteriaNullOrEmpty(criteria)) {\n            return Promise.reject(\n                new TypeORMError(\n                    `Empty criteria(s) are not allowed for the update method.`,\n                ),\n            )\n        }\n\n        if (OrmUtils.isPrimitiveCriteria(criteria)) {\n            return this.createQueryBuilder()\n                .update(target)\n                .set(partialEntity)\n                .whereInIds(criteria)\n                .execute()\n        } else {\n            return this.createQueryBuilder()\n                .update(target)\n                .set(partialEntity)\n                .where(criteria)\n                .execute()\n        }\n    }\n\n    /**\n     * Updates all entities of target type, setting fields from supplied partial entity.\n     * This is a primitive operation without cascades, relations or other operations included.\n     * Executes fast and efficient UPDATE query without WHERE clause.\n     *\n     * WARNING! This method updates ALL rows in the target table.\n     */\n    updateAll<Entity extends ObjectLiteral>(\n        target: EntityTarget<Entity>,\n        partialEntity: QueryDeepPartialEntity<Entity>,\n    ): Promise<UpdateResult> {\n        return this.createQueryBuilder()\n            .update(target)\n            .set(partialEntity)\n            .execute()\n    }\n\n    /**\n     * Deletes entities by a given condition(s).\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient DELETE query.\n     * Does not check if entity exist in the database.\n     * Condition(s) cannot be empty.\n     */\n    delete<Entity extends ObjectLiteral>(\n        targetOrEntity: EntityTarget<Entity>,\n        criteria:\n            | string\n            | string[]\n            | number\n            | number[]\n            | Date\n            | Date[]\n            | ObjectId\n            | ObjectId[]\n            | any,\n    ): Promise<DeleteResult> {\n        // if user passed empty criteria or empty list of criterias, then throw an error\n        if (OrmUtils.isCriteriaNullOrEmpty(criteria)) {\n            return Promise.reject(\n                new TypeORMError(\n                    `Empty criteria(s) are not allowed for the delete method.`,\n                ),\n            )\n        }\n\n        if (OrmUtils.isPrimitiveCriteria(criteria)) {\n            return this.createQueryBuilder()\n                .delete()\n                .from(targetOrEntity)\n                .whereInIds(criteria)\n                .execute()\n        } else {\n            return this.createQueryBuilder()\n                .delete()\n                .from(targetOrEntity)\n                .where(criteria)\n                .execute()\n        }\n    }\n\n    /**\n     * Deletes all entities of target type.\n     * This is a primitive operation without cascades, relations or other operations included.\n     * Executes fast and efficient DELETE query without WHERE clause.\n     *\n     * WARNING! This method deletes ALL rows in the target table.\n     */\n    deleteAll<Entity extends ObjectLiteral>(\n        targetOrEntity: EntityTarget<Entity>,\n    ): Promise<DeleteResult> {\n        return this.createQueryBuilder().delete().from(targetOrEntity).execute()\n    }\n\n    /**\n     * Records the delete date of entities by a given condition(s).\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient UPDATE query.\n     * Does not check if entity exist in the database.\n     * Condition(s) cannot be empty.\n     */\n    softDelete<Entity extends ObjectLiteral>(\n        targetOrEntity: EntityTarget<Entity>,\n        criteria:\n            | string\n            | string[]\n            | number\n            | number[]\n            | Date\n            | Date[]\n            | ObjectId\n            | ObjectId[]\n            | any,\n    ): Promise<UpdateResult> {\n        // if user passed empty criteria or empty list of criterias, then throw an error\n        if (OrmUtils.isCriteriaNullOrEmpty(criteria)) {\n            return Promise.reject(\n                new TypeORMError(\n                    `Empty criteria(s) are not allowed for the softDelete method.`,\n                ),\n            )\n        }\n\n        if (OrmUtils.isPrimitiveCriteria(criteria)) {\n            return this.createQueryBuilder()\n                .softDelete()\n                .from(targetOrEntity)\n                .whereInIds(criteria)\n                .execute()\n        } else {\n            return this.createQueryBuilder()\n                .softDelete()\n                .from(targetOrEntity)\n                .where(criteria)\n                .execute()\n        }\n    }\n\n    /**\n     * Restores entities by a given condition(s).\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient DELETE query.\n     * Does not check if entity exist in the database.\n     * Condition(s) cannot be empty.\n     */\n    restore<Entity extends ObjectLiteral>(\n        targetOrEntity: EntityTarget<Entity>,\n        criteria:\n            | string\n            | string[]\n            | number\n            | number[]\n            | Date\n            | Date[]\n            | ObjectId\n            | ObjectId[]\n            | any,\n    ): Promise<UpdateResult> {\n        // if user passed empty criteria or empty list of criterias, then throw an error\n        if (OrmUtils.isCriteriaNullOrEmpty(criteria)) {\n            return Promise.reject(\n                new TypeORMError(\n                    `Empty criteria(s) are not allowed for the restore method.`,\n                ),\n            )\n        }\n\n        if (OrmUtils.isPrimitiveCriteria(criteria)) {\n            return this.createQueryBuilder()\n                .restore()\n                .from(targetOrEntity)\n                .whereInIds(criteria)\n                .execute()\n        } else {\n            return this.createQueryBuilder()\n                .restore()\n                .from(targetOrEntity)\n                .where(criteria)\n                .execute()\n        }\n    }\n\n    /**\n     * Checks whether any entity exists with the given options.\n     */\n    exists<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        options?: FindManyOptions<Entity>,\n    ): Promise<boolean> {\n        const metadata = this.connection.getMetadata(entityClass)\n        return this.createQueryBuilder(\n            entityClass,\n            FindOptionsUtils.extractFindManyOptionsAlias(options) ||\n                metadata.name,\n        )\n            .setFindOptions(options || {})\n            .getExists()\n    }\n\n    /**\n     * Checks whether any entity exists with the given conditions.\n     */\n    async existsBy<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        where: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<boolean> {\n        const metadata = this.connection.getMetadata(entityClass)\n        return this.createQueryBuilder(entityClass, metadata.name)\n            .setFindOptions({ where })\n            .getExists()\n    }\n\n    /**\n     * Counts entities that match given options.\n     * Useful for pagination.\n     */\n    count<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        options?: FindManyOptions<Entity>,\n    ): Promise<number> {\n        const metadata = this.connection.getMetadata(entityClass)\n        return this.createQueryBuilder(\n            entityClass,\n            FindOptionsUtils.extractFindManyOptionsAlias(options) ||\n                metadata.name,\n        )\n            .setFindOptions(options || {})\n            .getCount()\n    }\n\n    /**\n     * Counts entities that match given conditions.\n     * Useful for pagination.\n     */\n    countBy<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        where: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<number> {\n        const metadata = this.connection.getMetadata(entityClass)\n        return this.createQueryBuilder(entityClass, metadata.name)\n            .setFindOptions({ where })\n            .getCount()\n    }\n\n    /**\n     * Return the SUM of a column\n     */\n    sum<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        columnName: PickKeysByType<Entity, number>,\n        where?: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<number | null> {\n        return this.callAggregateFun(entityClass, \"SUM\", columnName, where)\n    }\n\n    /**\n     * Return the AVG of a column\n     */\n    average<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        columnName: PickKeysByType<Entity, number>,\n        where?: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<number | null> {\n        return this.callAggregateFun(entityClass, \"AVG\", columnName, where)\n    }\n\n    /**\n     * Return the MIN of a column\n     */\n    minimum<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        columnName: PickKeysByType<Entity, number>,\n        where?: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<number | null> {\n        return this.callAggregateFun(entityClass, \"MIN\", columnName, where)\n    }\n\n    /**\n     * Return the MAX of a column\n     */\n    maximum<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        columnName: PickKeysByType<Entity, number>,\n        where?: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<number | null> {\n        return this.callAggregateFun(entityClass, \"MAX\", columnName, where)\n    }\n\n    private async callAggregateFun<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        fnName: \"SUM\" | \"AVG\" | \"MIN\" | \"MAX\",\n        columnName: PickKeysByType<Entity, number>,\n        where: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[] = {},\n    ): Promise<number | null> {\n        const metadata = this.connection.getMetadata(entityClass)\n        const column = metadata.columns.find(\n            (item) => item.propertyPath === columnName,\n        )\n        if (!column) {\n            throw new TypeORMError(\n                `Column \"${columnName}\" was not found in table \"${metadata.name}\"`,\n            )\n        }\n\n        const result = await this.createQueryBuilder(entityClass, metadata.name)\n            .setFindOptions({ where })\n            .select(\n                `${fnName}(${this.connection.driver.escape(\n                    column.databaseName,\n                )})`,\n                fnName,\n            )\n            .getRawOne()\n        return result[fnName] === null ? null : parseFloat(result[fnName])\n    }\n\n    /**\n     * Finds entities that match given find options.\n     */\n    async find<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        options?: FindManyOptions<Entity>,\n    ): Promise<Entity[]> {\n        const metadata = this.connection.getMetadata(entityClass)\n        return this.createQueryBuilder<Entity>(\n            entityClass as any,\n            FindOptionsUtils.extractFindManyOptionsAlias(options) ||\n                metadata.name,\n        )\n            .setFindOptions(options || {})\n            .getMany()\n    }\n\n    /**\n     * Finds entities that match given find options.\n     */\n    async findBy<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        where: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<Entity[]> {\n        const metadata = this.connection.getMetadata(entityClass)\n        return this.createQueryBuilder<Entity>(\n            entityClass as any,\n            metadata.name,\n        )\n            .setFindOptions({ where: where })\n            .getMany()\n    }\n\n    /**\n     * Finds entities that match given find options.\n     * Also counts all entities that match given conditions,\n     * but ignores pagination settings (from and take options).\n     */\n    findAndCount<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        options?: FindManyOptions<Entity>,\n    ): Promise<[Entity[], number]> {\n        const metadata = this.connection.getMetadata(entityClass)\n        return this.createQueryBuilder<Entity>(\n            entityClass as any,\n            FindOptionsUtils.extractFindManyOptionsAlias(options) ||\n                metadata.name,\n        )\n            .setFindOptions(options || {})\n            .getManyAndCount()\n    }\n\n    /**\n     * Finds entities that match given WHERE conditions.\n     * Also counts all entities that match given conditions,\n     * but ignores pagination settings (from and take options).\n     */\n    findAndCountBy<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        where: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<[Entity[], number]> {\n        const metadata = this.connection.getMetadata(entityClass)\n        return this.createQueryBuilder<Entity>(\n            entityClass as any,\n            metadata.name,\n        )\n            .setFindOptions({ where })\n            .getManyAndCount()\n    }\n\n    /**\n     * Finds entities with ids.\n     * Optionally find options or conditions can be applied.\n     *\n     * @deprecated use `findBy` method instead in conjunction with `In` operator, for example:\n     *\n     * .findBy({\n     *     id: In([1, 2, 3])\n     * })\n     */\n    async findByIds<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        ids: any[],\n    ): Promise<Entity[]> {\n        // if no ids passed, no need to execute a query - just return an empty array of values\n        if (!ids.length) return Promise.resolve([])\n\n        const metadata = this.connection.getMetadata(entityClass)\n        return this.createQueryBuilder<Entity>(\n            entityClass as any,\n            metadata.name,\n        )\n            .andWhereInIds(ids)\n            .getMany()\n    }\n\n    /**\n     * Finds first entity by a given find options.\n     * If entity was not found in the database - returns null.\n     */\n    async findOne<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        options: FindOneOptions<Entity>,\n    ): Promise<Entity | null> {\n        const metadata = this.connection.getMetadata(entityClass)\n\n        // prepare alias for built query\n        let alias: string = metadata.name\n        if (options && options.join) {\n            alias = options.join.alias\n        }\n\n        if (!options.where) {\n            throw new Error(\n                `You must provide selection conditions in order to find a single row.`,\n            )\n        }\n\n        // create query builder and apply find options\n        return this.createQueryBuilder<Entity>(entityClass, alias)\n            .setFindOptions({\n                ...options,\n                take: 1,\n            })\n            .getOne()\n    }\n\n    /**\n     * Finds first entity that matches given where condition.\n     * If entity was not found in the database - returns null.\n     */\n    async findOneBy<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        where: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<Entity | null> {\n        const metadata = this.connection.getMetadata(entityClass)\n\n        // create query builder and apply find options\n        return this.createQueryBuilder<Entity>(entityClass, metadata.name)\n            .setFindOptions({\n                where,\n                take: 1,\n            })\n            .getOne()\n    }\n\n    /**\n     * Finds first entity that matches given id.\n     * If entity was not found in the database - returns null.\n     *\n     * @deprecated use `findOneBy` method instead in conjunction with `In` operator, for example:\n     *\n     * .findOneBy({\n     *     id: 1 // where \"id\" is your primary column name\n     * })\n     */\n    async findOneById<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        id: number | string | Date | ObjectId,\n    ): Promise<Entity | null> {\n        const metadata = this.connection.getMetadata(entityClass)\n\n        // create query builder and apply find options\n        return this.createQueryBuilder<Entity>(entityClass, metadata.name)\n            .setFindOptions({\n                take: 1,\n            })\n            .whereInIds(metadata.ensureEntityIdMap(id))\n            .getOne()\n    }\n\n    /**\n     * Finds first entity by a given find options.\n     * If entity was not found in the database - rejects with error.\n     */\n    async findOneOrFail<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        options: FindOneOptions<Entity>,\n    ): Promise<Entity> {\n        return this.findOne<Entity>(entityClass as any, options).then(\n            (value) => {\n                if (value === null) {\n                    return Promise.reject(\n                        new EntityNotFoundError(entityClass, options),\n                    )\n                }\n                return Promise.resolve(value)\n            },\n        )\n    }\n\n    /**\n     * Finds first entity that matches given where condition.\n     * If entity was not found in the database - rejects with error.\n     */\n    async findOneByOrFail<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        where: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<Entity> {\n        return this.findOneBy<Entity>(entityClass as any, where).then(\n            (value) => {\n                if (value === null) {\n                    return Promise.reject(\n                        new EntityNotFoundError(entityClass, where),\n                    )\n                }\n                return Promise.resolve(value)\n            },\n        )\n    }\n\n    /**\n     * Clears all the data from the given table (truncates/drops it).\n     *\n     * Note: this method uses TRUNCATE and may not work as you expect in transactions on some platforms.\n     * @see https://stackoverflow.com/a/5972738/925151\n     */\n    async clear<Entity>(entityClass: EntityTarget<Entity>): Promise<void> {\n        const metadata = this.connection.getMetadata(entityClass)\n        const queryRunner =\n            this.queryRunner || this.connection.createQueryRunner()\n        try {\n            return await queryRunner.clearTable(metadata.tablePath) // await is needed here because we are using finally\n        } finally {\n            if (!this.queryRunner) await queryRunner.release()\n        }\n    }\n\n    /**\n     * Increments some column by provided value of the entities matched given conditions.\n     */\n    async increment<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        conditions: any,\n        propertyPath: string,\n        value: number | string,\n    ): Promise<UpdateResult> {\n        const metadata = this.connection.getMetadata(entityClass)\n        const column = metadata.findColumnWithPropertyPath(propertyPath)\n        if (!column)\n            throw new TypeORMError(\n                `Column ${propertyPath} was not found in ${metadata.targetName} entity.`,\n            )\n\n        if (isNaN(Number(value)))\n            throw new TypeORMError(`Value \"${value}\" is not a number.`)\n\n        // convert possible embedded path \"social.likes\" into object { social: { like: () => value } }\n        const values: QueryDeepPartialEntity<Entity> = propertyPath\n            .split(\".\")\n            .reduceRight(\n                (value, key) => ({ [key]: value } as any),\n                () =>\n                    this.connection.driver.escape(column.databaseName) +\n                    \" + \" +\n                    value,\n            )\n\n        return this.createQueryBuilder<Entity>(entityClass as any, \"entity\")\n            .update(entityClass)\n            .set(values)\n            .where(conditions)\n            .execute()\n    }\n\n    /**\n     * Decrements some column by provided value of the entities matched given conditions.\n     */\n    async decrement<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        conditions: any,\n        propertyPath: string,\n        value: number | string,\n    ): Promise<UpdateResult> {\n        const metadata = this.connection.getMetadata(entityClass)\n        const column = metadata.findColumnWithPropertyPath(propertyPath)\n        if (!column)\n            throw new TypeORMError(\n                `Column ${propertyPath} was not found in ${metadata.targetName} entity.`,\n            )\n\n        if (isNaN(Number(value)))\n            throw new TypeORMError(`Value \"${value}\" is not a number.`)\n\n        // convert possible embedded path \"social.likes\" into object { social: { like: () => value } }\n        const values: QueryDeepPartialEntity<Entity> = propertyPath\n            .split(\".\")\n            .reduceRight(\n                (value, key) => ({ [key]: value } as any),\n                () =>\n                    this.connection.driver.escape(column.databaseName) +\n                    \" - \" +\n                    value,\n            )\n\n        return this.createQueryBuilder<Entity>(entityClass as any, \"entity\")\n            .update(entityClass)\n            .set(values)\n            .where(conditions)\n            .execute()\n    }\n\n    /**\n     * Gets repository for the given entity class or name.\n     * If single database connection mode is used, then repository is obtained from the\n     * repository aggregator, where each repository is individually created for this entity manager.\n     * When single database connection is not used, repository is being obtained from the connection.\n     */\n    getRepository<Entity extends ObjectLiteral>(\n        target: EntityTarget<Entity>,\n    ): Repository<Entity> {\n        // find already created repository instance and return it if found\n        const repoFromMap = this.repositories.get(target)\n        if (repoFromMap) return repoFromMap\n\n        // if repository was not found then create it, store its instance and return it\n        if (this.connection.driver.options.type === \"mongodb\") {\n            const newRepository = new MongoRepository(\n                target,\n                this,\n                this.queryRunner,\n            )\n            this.repositories.set(target, newRepository)\n            return newRepository\n        } else {\n            const newRepository = new Repository<any>(\n                target,\n                this,\n                this.queryRunner,\n            )\n            this.repositories.set(target, newRepository)\n            return newRepository\n        }\n    }\n\n    /**\n     * Gets tree repository for the given entity class or name.\n     * If single database connection mode is used, then repository is obtained from the\n     * repository aggregator, where each repository is individually created for this entity manager.\n     * When single database connection is not used, repository is being obtained from the connection.\n     */\n    getTreeRepository<Entity extends ObjectLiteral>(\n        target: EntityTarget<Entity>,\n    ): TreeRepository<Entity> {\n        // tree tables aren't supported by some drivers (mongodb)\n        if (this.connection.driver.treeSupport === false)\n            throw new TreeRepositoryNotSupportedError(this.connection.driver)\n\n        // find already created repository instance and return it if found\n        const repository = this.treeRepositories.find(\n            (repository) => repository.target === target,\n        )\n        if (repository) return repository\n\n        // check if repository is real tree repository\n        const newRepository = new TreeRepository(target, this, this.queryRunner)\n        this.treeRepositories.push(newRepository)\n        return newRepository\n    }\n\n    /**\n     * Gets mongodb repository for the given entity class.\n     */\n    getMongoRepository<Entity extends ObjectLiteral>(\n        target: EntityTarget<Entity>,\n    ): MongoRepository<Entity> {\n        return this.connection.getMongoRepository<Entity>(target)\n    }\n\n    /**\n     * Creates a new repository instance out of a given Repository and\n     * sets current EntityManager instance to it. Used to work with custom repositories\n     * in transactions.\n     */\n    withRepository<Entity extends ObjectLiteral, R extends Repository<any>>(\n        repository: R & Repository<Entity>,\n    ): R {\n        const repositoryConstructor =\n            repository.constructor as typeof Repository\n        const { target, manager, queryRunner, ...otherRepositoryProperties } =\n            repository\n        return Object.assign(\n            new repositoryConstructor(repository.target, this) as R,\n            {\n                ...otherRepositoryProperties,\n            },\n        )\n    }\n\n    /**\n     * Gets custom entity repository marked with @EntityRepository decorator.\n     *\n     * @deprecated use Repository.extend to create custom repositories\n     */\n    getCustomRepository<T>(customRepository: ObjectType<T>): T {\n        const entityRepositoryMetadataArgs =\n            getMetadataArgsStorage().entityRepositories.find((repository) => {\n                return (\n                    repository.target ===\n                    (typeof customRepository === \"function\"\n                        ? customRepository\n                        : (customRepository as any).constructor)\n                )\n            })\n        if (!entityRepositoryMetadataArgs)\n            throw new CustomRepositoryNotFoundError(customRepository)\n\n        const entityMetadata = entityRepositoryMetadataArgs.entity\n            ? this.connection.getMetadata(entityRepositoryMetadataArgs.entity)\n            : undefined\n        const entityRepositoryInstance =\n            new (entityRepositoryMetadataArgs.target as any)(\n                this,\n                entityMetadata,\n            )\n\n        // NOTE: dynamic access to protected properties. We need this to prevent unwanted properties in those classes to be exposed,\n        // however we need these properties for internal work of the class\n        if (entityRepositoryInstance instanceof AbstractRepository) {\n            if (!(entityRepositoryInstance as any)[\"manager\"])\n                (entityRepositoryInstance as any)[\"manager\"] = this\n        } else {\n            if (!entityMetadata)\n                throw new CustomRepositoryCannotInheritRepositoryError(\n                    customRepository,\n                )\n            ;(entityRepositoryInstance as any)[\"manager\"] = this\n            ;(entityRepositoryInstance as any)[\"metadata\"] = entityMetadata\n        }\n\n        return entityRepositoryInstance\n    }\n\n    /**\n     * Releases all resources used by entity manager.\n     * This is used when entity manager is created with a single query runner,\n     * and this single query runner needs to be released after job with entity manager is done.\n     */\n    async release(): Promise<void> {\n        if (!this.queryRunner) throw new NoNeedToReleaseEntityManagerError()\n\n        return this.queryRunner.release()\n    }\n}\n"], "sourceRoot": ".."}