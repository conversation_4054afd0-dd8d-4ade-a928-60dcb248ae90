{"version": 3, "sources": ["../../src/entity-schema/EntitySchemaRelationOptions.ts"], "names": [], "mappings": "", "file": "EntitySchemaRelationOptions.js", "sourcesContent": ["import { JoinColumnOptions } from \"../decorator/options/JoinColumnOptions\"\nimport { RelationType } from \"../metadata/types/RelationTypes\"\nimport { JoinTableMultipleColumnsOptions } from \"../decorator/options/JoinTableMultipleColumnsOptions\"\nimport { DeferrableType } from \"../metadata/types/DeferrableType\"\nimport { OnDeleteType } from \"../metadata/types/OnDeleteType\"\nimport { OnUpdateType } from \"../metadata/types/OnUpdateType\"\nimport { JoinTableOptions } from \"../decorator/options/JoinTableOptions\"\nimport { EntityTarget } from \"../common/EntityTarget\"\n\nexport interface EntitySchemaRelationOptions {\n    /**\n     * Indicates with which entity this relation is made.\n     */\n    target: EntityTarget<any>\n\n    /**\n     * Type of relation. Can be one of the value of the RelationTypes class.\n     */\n    type: RelationType\n\n    /**\n     * Inverse side of the relation.\n     */\n    inverseSide?: string\n\n    /**\n     * Indicates if this relation will be lazily loaded.\n     */\n    lazy?: boolean\n\n    /**\n     * Indicates if this relation will be eagerly loaded.\n     */\n    eager?: boolean\n\n    /**\n     * Indicates if persistence is enabled for the relation.\n     * By default its enabled, but if you want to avoid any changes in the relation to be reflected in the database you can disable it.\n     * If its disabled you can only change a relation from inverse side of a relation or using relation query builder functionality.\n     * This is useful for performance optimization since its disabling avoid multiple extra queries during entity save.\n     */\n    persistence?: boolean\n\n    /**\n     * Indicates if this relation will be a primary key.\n     * Can be used only for many-to-one and owner one-to-one relations.\n     */\n    primary?: boolean\n\n    /**\n     * Indicates whether foreign key constraints will be created for join columns.\n     * Can be used only for many-to-one and owner one-to-one relations.\n     * Defaults to true.\n     */\n    createForeignKeyConstraints?: boolean\n\n    /**\n     * Join table options of this column. If set to true then it simply means that it has a join table.\n     */\n    joinTable?: boolean | JoinTableOptions | JoinTableMultipleColumnsOptions\n\n    /**\n     * Join column options of this column. If set to true then it simply means that it has a join column.\n     */\n    joinColumn?: boolean | JoinColumnOptions | JoinColumnOptions[]\n\n    /**\n     * Indicates if this is a parent (can be only many-to-one relation) relation in the tree tables.\n     */\n    treeParent?: boolean\n\n    /**\n     * Indicates if this is a children (can be only one-to-many relation) relation in the tree tables.\n     */\n    treeChildren?: boolean\n\n    /**\n     * If set to true then it means that related object can be allowed to be inserted / updated / removed to the db.\n     * This is option a shortcut if you would like to set cascadeInsert, cascadeUpdate and cascadeRemove to true.\n     */\n    cascade?:\n        | boolean\n        | (\"insert\" | \"update\" | \"remove\" | \"soft-remove\" | \"recover\")[]\n\n    /**\n     * Default database value.\n     */\n    default?: any\n\n    /**\n     * Indicates if relation column value can be nullable or not.\n     */\n    nullable?: boolean\n\n    /**\n     * Database cascade action on delete.\n     */\n    onDelete?: OnDeleteType\n\n    /**\n     * Database cascade action on update.\n     */\n    onUpdate?: OnUpdateType\n\n    /**\n     * Indicate if foreign key constraints can be deferred.\n     */\n    deferrable?: DeferrableType\n\n    /**\n     * When a parent is saved (with cascading but) without a child row that still exists in database, this will control what shall happen to them.\n     * delete will remove these rows from database. nullify will remove the relation key.\n     * skip will keep the relation intact. Removal of related item is only possible through its own repo.\n     */\n    orphanedRowAction?: \"nullify\" | \"delete\" | \"soft-delete\" | \"disable\"\n}\n"], "sourceRoot": ".."}