{"version": 3, "sources": ["../../src/entity-schema/EntitySchemaIndexOptions.ts"], "names": [], "mappings": "", "file": "EntitySchemaIndexOptions.js", "sourcesContent": ["export interface EntitySchemaIndexOptions {\n    /**\n     * Index name.\n     */\n    name?: string\n\n    /**\n     * Index column names.\n     */\n    columns?: ((object?: any) => any[] | { [key: string]: number }) | string[]\n\n    /**\n     * Indicates if index must sync with database index.\n     */\n    synchronize?: boolean\n\n    /**\n     * If true, the index only references documents with the specified field.\n     * These indexes use less space but behave differently in some situations (particularly sorts).\n     * This option is only supported for mongodb database.\n     */\n    sparse?: boolean\n\n    /**\n     * Indicates if this index must be unique or not.\n     */\n    unique?: boolean\n\n    /**\n     * The SPATIAL modifier indexes the entire column and does not allow indexed columns to contain NULL values.\n     * Works only in MySQL and PostgreSQL.\n     */\n    spatial?: boolean\n\n    /**\n     * The FULLTEXT modifier indexes the entire column and does not allow prefixing.\n     * Works only in MySQL.\n     */\n    fulltext?: boolean\n\n    /**\n     * NULL_FILTERED indexes are particularly useful for indexing sparse columns, where most rows contain a NULL value.\n     * In these cases, the NULL_FILTERED index can be considerably smaller and more efficient to maintain than\n     * a normal index that includes NULL values.\n     *\n     * Works only in Spanner.\n     */\n    nullFiltered?: boolean\n\n    /**\n     * Fulltext parser.\n     * Works only in MySQL.\n     */\n    parser?: string\n\n    /**\n     * Index filter condition.\n     */\n    where?: string\n}\n"], "sourceRoot": ".."}