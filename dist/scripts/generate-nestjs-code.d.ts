#!/usr/bin/env ts-node
interface GenerationConfig {
    openApiPath: string;
    outputDir: string;
    templatesDir: string;
}
declare class NestJSCodeGenerator {
    private spec;
    private config;
    constructor(config: GenerationConfig);
    private loadOpenAPISpec;
    generateAll(): Promise<void>;
    private generateDTOs;
    private generateDTOClass;
    private generateProperty;
    private generatePropertyDecorators;
    private generateValidationDecorators;
    private getValidationImports;
    private getTypeScriptType;
    private generateEntities;
    private generateControllers;
    private generateServices;
    private generateModules;
    private isReferenceObject;
    private toCamelCase;
    private toPascalCase;
    private ensureDirectoryExists;
}
export { NestJSCodeGenerator };
