#!/usr/bin/env ts-node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const fs = require("fs");
const path = require("path");
generateControllers();
void {
    console, : .log('🎮 Generating controllers...'),
    this: .modules.forEach((moduleInfo, moduleName) => {
        if (moduleInfo.endpoints.length === 0)
            return;
        const className = `${this.capitalize(moduleName)}Controller`;
        const serviceName = `${this.capitalize(moduleName)}Service`;
        let imports = `import { Controller, Get, Post, Put, Patch, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { ${serviceName} } from './${moduleName}.service';
`;
        moduleInfo.schemas.forEach(schemaName => {
            const fileName = this.toKebabCase(schemaName);
            imports += `import { ${schemaName} } from './dto/${fileName}.dto';\n`;
        });
        let controllerContent = `
@ApiTags('${moduleInfo.endpoints[0]?.tags[0] || moduleName}')
@Controller('${moduleName}')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ${className} {
  constructor(private readonly ${moduleName}Service: ${serviceName}) {}
`;
        moduleInfo.endpoints.forEach(endpoint => {
            const methodName = this.getMethodNameFromOperationId(endpoint.operationId);
            const httpMethod = endpoint.method.toLowerCase();
            const pathParams = this.extractPathParams(endpoint.path);
            const queryParams = endpoint.parameters?.filter(p => p.in === 'query') || [];
            controllerContent += `
  @${this.capitalize(httpMethod)}('${this.convertPathToNestJS(endpoint.path)}')
  @ApiOperation({ summary: '${endpoint.summary}' })
  @ApiResponse({ status: 200, description: 'Success' })
  async ${methodName}(`;
            const params = [];
            pathParams.forEach(param => {
                params.push(`@Param('${param}') ${param}: string`);
            });
            if (queryParams.length > 0) {
                params.push(`@Query() query: any`);
            }
            if (['post', 'put', 'patch'].includes(httpMethod) && endpoint.requestBody) {
                params.push(`@Body() body: any`);
            }
            controllerContent += params.join(', ');
            controllerContent += `) {
    return this.${moduleName}Service.${methodName}(${this.generateServiceCallParams(endpoint, pathParams)});
  }
`;
        });
        controllerContent += '}\n';
        const fullContent = imports + controllerContent;
        const controllerPath = path.join(this.srcDir, moduleName, `${moduleName}.controller.ts`);
        this.ensureDirectoryExists(path.dirname(controllerPath));
        fs.writeFileSync(controllerPath, fullContent);
        console.log(`  ✓ Generated ${moduleName}/${moduleName}.controller.ts`);
    })
};
generateServices();
void {
    console, : .log('⚙️  Generating services...'),
    this: .modules.forEach((moduleInfo, moduleName) => {
        if (moduleInfo.endpoints.length === 0)
            return;
        const className = `${this.capitalize(moduleName)}Service`;
        let serviceContent = `import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class ${className} {
  constructor(
    // Add repository injections here when entities are generated
  ) {}
`;
        moduleInfo.endpoints.forEach(endpoint => {
            const methodName = this.getMethodNameFromOperationId(endpoint.operationId);
            const pathParams = this.extractPathParams(endpoint.path);
            serviceContent += `
  async ${methodName}(${this.generateServiceMethodParams(endpoint, pathParams)}): Promise<any> {
    // TODO: Implement ${endpoint.summary}
    // Method: ${endpoint.method} ${endpoint.path}
    return {};
  }
`;
        });
        serviceContent += '}\n';
        const servicePath = path.join(this.srcDir, moduleName, `${moduleName}.service.ts`);
        this.ensureDirectoryExists(path.dirname(servicePath));
        fs.writeFileSync(servicePath, serviceContent);
        console.log(`  ✓ Generated ${moduleName}/${moduleName}.service.ts`);
    })
};
generateModules();
void {
    console, : .log('📦 Generating modules...'),
    this: .modules.forEach((moduleInfo, moduleName) => {
        if (moduleInfo.endpoints.length === 0)
            return;
        const className = `${this.capitalize(moduleName)}Module`;
        const controllerName = `${this.capitalize(moduleName)}Controller`;
        const serviceName = `${this.capitalize(moduleName)}Service`;
        let moduleContent = `import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ${controllerName} } from './${moduleName}.controller';
import { ${serviceName} } from './${moduleName}.service';

@Module({
  imports: [
    // TypeOrmModule.forFeature([]), // Add entities here
  ],
  controllers: [${controllerName}],
  providers: [${serviceName}],
  exports: [${serviceName}],
})
export class ${className} {}
`;
        const modulePath = path.join(this.srcDir, moduleName, `${moduleName}.module.ts`);
        this.ensureDirectoryExists(path.dirname(modulePath));
        fs.writeFileSync(modulePath, moduleContent);
        console.log(`  ✓ Generated ${moduleName}/${moduleName}.module.ts`);
    })
};
updateAppModule();
void {
    console, : .log('🔧 Updating app.module.ts...'),
    const: moduleImports = Array.from(this.modules.keys())
        .filter(name => this.modules.get(name).endpoints.length > 0)
        .map(name => `${this.capitalize(name)}Module`)
        .join(', '),
    const: importStatements = Array.from(this.modules.keys())
        .filter(name => this.modules.get(name).endpoints.length > 0)
        .map(name => `import { ${this.capitalize(name)}Module } from '@/${name}/${name}.module';`)
        .join('\n'),
    let, appModuleContent = `import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ThrottlerModule } from '@nestjs/throttler';
${importStatements}

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DATABASE_HOST || 'localhost',
      port: parseInt(process.env.DATABASE_PORT) || 5432,
      username: process.env.DATABASE_USER || 'postgres',
      password: process.env.DATABASE_PASSWORD || 'password',
      database: process.env.DATABASE_NAME || 'rsglider',
      autoLoadEntities: true,
      synchronize: process.env.NODE_ENV !== 'production',
    }),
    ThrottlerModule.forRoot([{
      ttl: 60000,
      limit: 10,
    }]),
    ${moduleImports},
  ],
})
export class AppModule {}
`,
    const: appModulePath = path.join(this.srcDir, 'app.module.ts'),
    fs, : .writeFileSync(appModulePath, appModuleContent),
    console, : .log('  ✓ Updated app.module.ts')
};
getMethodNameFromOperationId(operationId, string);
string;
{
    return operationId.replace(/[^a-zA-Z0-9]/g, '').replace(/^./, str => str.toLowerCase());
}
extractPathParams(path, string);
string[];
{
    const matches = path.match(/\{([^}]+)\}/g);
    return matches ? matches.map(match => match.slice(1, -1)) : [];
}
convertPathToNestJS(path, string);
string;
{
    return path.replace(/\{([^}]+)\}/g, ':$1').replace(/^\/[^\/]+/, '');
}
generateServiceCallParams(endpoint, EndpointInfo, pathParams, string[]);
string;
{
    const params = [];
    pathParams.forEach(param => {
        params.push(param);
    });
    if (endpoint.parameters?.some(p => p.in === 'query')) {
        params.push('query');
    }
    if (['POST', 'PUT', 'PATCH'].includes(endpoint.method) && endpoint.requestBody) {
        params.push('body');
    }
    return params.join(', ');
}
generateServiceMethodParams(endpoint, EndpointInfo, pathParams, string[]);
string;
{
    const params = [];
    pathParams.forEach(param => {
        params.push(`${param}: string`);
    });
    if (endpoint.parameters?.some(p => p.in === 'query')) {
        params.push('query?: any');
    }
    if (['POST', 'PUT', 'PATCH'].includes(endpoint.method) && endpoint.requestBody) {
        params.push('body?: any');
    }
    return params.join(', ');
}
capitalize(str, string);
string;
{
    return str.charAt(0).toUpperCase() + str.slice(1);
}
async function main() {
    const generator = new AdvancedAPIGenerator();
    await generator.generate();
}
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=advanced-generator.js.map