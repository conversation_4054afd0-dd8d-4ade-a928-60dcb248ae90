#!/usr/bin/env ts-node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const fs = require("fs");
const yaml = require("js-yaml");
const path = require("path");
class CompleteAPIGenerator {
    constructor() {
        this.modules = new Map();
        this.srcDir = path.join(process.cwd(), 'src');
    }
    async generate() {
        console.log('🚀 Generating COMPLETE NestJS API from OpenAPI spec...');
        const openApiPath = path.join(process.cwd(), 'api-docs', 'openapi.yaml');
        const openApiContent = fs.readFileSync(openApiPath, 'utf8');
        this.spec = yaml.load(openApiContent);
        this.parseEndpoints();
        this.generateDTOs();
        this.generateEntities();
        this.generateControllers();
        this.generateServices();
        this.generateModules();
        this.updateAppModule();
        console.log('✅ Complete API generated successfully!');
    }
    parseEndpoints() {
        console.log('📊 Parsing OpenAPI endpoints...');
        Object.entries(this.spec.paths || {}).forEach(([pathStr, pathItem]) => {
            if (!pathItem)
                return;
            Object.entries(pathItem).forEach(([method, operation]) => {
                if (!operation || typeof operation !== 'object' || !('operationId' in operation))
                    return;
                const op = operation;
                if (!op.operationId)
                    return;
                const tags = op.tags || ['default'];
                const moduleName = this.getModuleFromTags(tags);
                if (!this.modules.has(moduleName)) {
                    this.modules.set(moduleName, { name: moduleName, endpoints: [], schemas: [] });
                }
                const moduleInfo = this.modules.get(moduleName);
                moduleInfo.endpoints.push({
                    path: pathStr,
                    method: method.toUpperCase(),
                    operationId: op.operationId,
                    summary: op.summary || '',
                    tags,
                    parameters: op.parameters,
                    requestBody: op.requestBody,
                    responses: op.responses
                });
            });
        });
        if (this.spec.components?.schemas) {
            Object.keys(this.spec.components.schemas).forEach(schemaName => {
                const moduleName = this.getModuleFromSchemaName(schemaName);
                if (!this.modules.has(moduleName)) {
                    this.modules.set(moduleName, { name: moduleName, endpoints: [], schemas: [] });
                }
                this.modules.get(moduleName).schemas.push(schemaName);
            });
        }
        console.log(`  ✓ Found ${this.modules.size} modules with ${Array.from(this.modules.values()).reduce((sum, m) => sum + m.endpoints.length, 0)} endpoints`);
    }
    getModuleFromTags(tags) {
        const tag = tags[0].toLowerCase();
        if (tag.includes('auth'))
            return 'auth';
        if (tag.includes('user') && tag.includes('profile'))
            return 'users';
        if (tag.includes('admin'))
            return 'admin';
        if (tag.includes('store'))
            return 'store';
        if (tag.includes('developer'))
            return 'developer';
        if (tag.includes('webhook'))
            return 'webhooks';
        return 'users';
    }
    getModuleFromSchemaName(schemaName) {
        const lower = schemaName.toLowerCase();
        if (lower.includes('auth') || lower.includes('login') || lower.includes('register') || lower.includes('token'))
            return 'auth';
        if (lower.includes('admin'))
            return 'admin';
        if (lower.includes('store') || lower.includes('cart') || lower.includes('pricing') || lower.includes('subscription'))
            return 'store';
        if (lower.includes('developer') || lower.includes('repository') || lower.includes('payout') || lower.includes('gitea'))
            return 'developer';
        if (lower.includes('webhook'))
            return 'webhooks';
        if (lower.includes('user'))
            return 'users';
        return 'users';
    }
    generateDTOs() {
        console.log('📝 Generating DTOs...');
        if (!this.spec.components?.schemas)
            return;
        Object.entries(this.spec.components.schemas).forEach(([schemaName, schema]) => {
            const schemaObj = schema;
            const moduleName = this.getModuleFromSchemaName(schemaName);
            const modulePath = path.join(this.srcDir, moduleName);
            this.ensureDirectoryExists(modulePath);
            this.ensureDirectoryExists(path.join(modulePath, 'dto'));
            const dtoContent = this.generateDTO(schemaName, schemaObj);
            const fileName = this.toKebabCase(schemaName) + '.dto.ts';
            const dtoPath = path.join(modulePath, 'dto', fileName);
            fs.writeFileSync(dtoPath, dtoContent);
            console.log(`  ✓ Generated ${moduleName}/${fileName}`);
        });
    }
    generateDTO(schemaName, schema) {
        let content = `import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class ${schemaName} {
`;
        if (schema.properties) {
            Object.entries(schema.properties).forEach(([propName, propSchema]) => {
                const property = propSchema;
                const isRequired = schema.required?.includes(propName) || false;
                content += `  @ApiProperty(${!isRequired ? '{ required: false }' : ''})\n`;
                if (!isRequired) {
                    content += `  @IsOptional()\n`;
                }
                else {
                    content += `  @IsNotEmpty()\n`;
                }
                if (property.type === 'string') {
                    content += `  @IsString()\n`;
                    if (property.format === 'email') {
                        content += `  @IsEmail()\n`;
                    }
                }
                else if (property.type === 'number' || property.type === 'integer') {
                    content += `  @IsNumber()\n`;
                }
                else if (property.type === 'boolean') {
                    content += `  @IsBoolean()\n`;
                }
                else if (property.type === 'array') {
                    content += `  @IsArray()\n`;
                }
                const tsType = this.getTypeScriptType(property);
                const optional = isRequired ? '' : '?';
                content += `  ${propName}${optional}: ${tsType};\n\n`;
            });
        }
        content += '}\n';
        return content;
    }
    generateEntities() {
        console.log('🗄️  Generating TypeORM entities...');
        const userEntityContent = `import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  @Index()
  email: string;

  @Column()
  password: string;

  @Column({ nullable: true })
  name?: string;

  @Column('simple-array', { default: 'user' })
  roles: string[];

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  emailVerified: boolean;

  @Column({ nullable: true })
  emailVerificationToken?: string;

  @Column({ nullable: true })
  passwordResetToken?: string;

  @Column({ type: 'timestamp', nullable: true })
  passwordResetExpires?: Date;

  @Column({ nullable: true })
  twoFactorSecret?: string;

  @Column({ default: false })
  twoFactorEnabled: boolean;

  @Column({ type: 'timestamp', nullable: true })
  lastLoginAt?: Date;

  @Column({ nullable: true })
  lastLoginIp?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
`;
        const userEntityPath = path.join(this.srcDir, 'users', 'entities', 'user.entity.ts');
        this.ensureDirectoryExists(path.dirname(userEntityPath));
        fs.writeFileSync(userEntityPath, userEntityContent);
        console.log('  ✓ Generated users/entities/user.entity.ts');
    }
    getTypeScriptType(schema) {
        switch (schema.type) {
            case 'string': return 'string';
            case 'number':
            case 'integer': return 'number';
            case 'boolean': return 'boolean';
            case 'array':
                if (schema.items) {
                    const itemType = this.getTypeScriptType(schema.items);
                    return `${itemType}[]`;
                }
                return 'any[]';
            case 'object': return 'object';
            default: return 'any';
        }
    }
    toKebabCase(str) {
        return str
            .replace(/([a-z0-9])([A-Z])/g, '$1-$2')
            .toLowerCase()
            .replace(/^-/, '');
    }
    generateControllers() {
        console.log('🎮 Generating controllers...');
        this.modules.forEach((moduleInfo, moduleName) => {
            if (moduleInfo.endpoints.length === 0)
                return;
            const className = `${this.capitalize(moduleName)}Controller`;
            const serviceName = `${this.capitalize(moduleName)}Service`;
            let imports = `import { Controller, Get, Post, Put, Patch, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { ${serviceName} } from './${moduleName}.service';
`;
            moduleInfo.schemas.forEach(schemaName => {
                const fileName = this.toKebabCase(schemaName);
                imports += `import { ${schemaName} } from './dto/${fileName}.dto';\n`;
            });
            let controllerContent = `
@ApiTags('${moduleInfo.endpoints[0]?.tags[0] || moduleName}')
@Controller('${moduleName}')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ${className} {
  constructor(private readonly ${moduleName}Service: ${serviceName}) {}
`;
            moduleInfo.endpoints.forEach(endpoint => {
                const methodName = this.getMethodNameFromOperationId(endpoint.operationId);
                const httpMethod = endpoint.method.toLowerCase();
                const pathParams = this.extractPathParams(endpoint.path);
                const queryParams = endpoint.parameters?.filter(p => p.in === 'query') || [];
                controllerContent += `
  @${this.capitalize(httpMethod)}('${this.convertPathToNestJS(endpoint.path)}')
  @ApiOperation({ summary: '${endpoint.summary}' })
  @ApiResponse({ status: 200, description: 'Success' })
  async ${methodName}(`;
                const params = [];
                pathParams.forEach(param => {
                    params.push(`@Param('${param}') ${param}: string`);
                });
                if (queryParams.length > 0) {
                    params.push(`@Query() query: any`);
                }
                if (['post', 'put', 'patch'].includes(httpMethod) && endpoint.requestBody) {
                    params.push(`@Body() body: any`);
                }
                controllerContent += params.join(', ');
                controllerContent += `) {
    return this.${moduleName}Service.${methodName}(${this.generateServiceCallParams(endpoint, pathParams)});
  }
`;
            });
            controllerContent += '}\n';
            const fullContent = imports + controllerContent;
            const controllerPath = path.join(this.srcDir, moduleName, `${moduleName}.controller.ts`);
            this.ensureDirectoryExists(path.dirname(controllerPath));
            fs.writeFileSync(controllerPath, fullContent);
            console.log(`  ✓ Generated ${moduleName}/${moduleName}.controller.ts`);
        });
    }
    generateServices() {
        console.log('⚙️  Generating services...');
        this.modules.forEach((moduleInfo, moduleName) => {
            if (moduleInfo.endpoints.length === 0)
                return;
            const className = `${this.capitalize(moduleName)}Service`;
            let serviceContent = `import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class ${className} {
  constructor(
    // Add repository injections here when entities are generated
  ) {}
`;
            moduleInfo.endpoints.forEach(endpoint => {
                const methodName = this.getMethodNameFromOperationId(endpoint.operationId);
                const pathParams = this.extractPathParams(endpoint.path);
                serviceContent += `
  async ${methodName}(${this.generateServiceMethodParams(endpoint, pathParams)}): Promise<any> {
    // TODO: Implement ${endpoint.summary}
    // Method: ${endpoint.method} ${endpoint.path}
    return {};
  }
`;
            });
            serviceContent += '}\n';
            const servicePath = path.join(this.srcDir, moduleName, `${moduleName}.service.ts`);
            this.ensureDirectoryExists(path.dirname(servicePath));
            fs.writeFileSync(servicePath, serviceContent);
            console.log(`  ✓ Generated ${moduleName}/${moduleName}.service.ts`);
        });
    }
    generateModules() {
        console.log('📦 Generating modules...');
        this.modules.forEach((moduleInfo, moduleName) => {
            if (moduleInfo.endpoints.length === 0)
                return;
            const className = `${this.capitalize(moduleName)}Module`;
            const controllerName = `${this.capitalize(moduleName)}Controller`;
            const serviceName = `${this.capitalize(moduleName)}Service`;
            let moduleContent = `import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ${controllerName} } from './${moduleName}.controller';
import { ${serviceName} } from './${moduleName}.service';

@Module({
  imports: [
    // TypeOrmModule.forFeature([]), // Add entities here
  ],
  controllers: [${controllerName}],
  providers: [${serviceName}],
  exports: [${serviceName}],
})
export class ${className} {}
`;
            const modulePath = path.join(this.srcDir, moduleName, `${moduleName}.module.ts`);
            this.ensureDirectoryExists(path.dirname(modulePath));
            fs.writeFileSync(modulePath, moduleContent);
            console.log(`  ✓ Generated ${moduleName}/${moduleName}.module.ts`);
        });
    }
    updateAppModule() {
        console.log('🔧 Updating app.module.ts...');
        const moduleImports = Array.from(this.modules.keys())
            .filter(name => this.modules.get(name).endpoints.length > 0)
            .map(name => `${this.capitalize(name)}Module`)
            .join(', ');
        const importStatements = Array.from(this.modules.keys())
            .filter(name => this.modules.get(name).endpoints.length > 0)
            .map(name => `import { ${this.capitalize(name)}Module } from '@/${name}/${name}.module';`)
            .join('\n');
        let appModuleContent = `import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ThrottlerModule } from '@nestjs/throttler';
${importStatements}

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DATABASE_HOST || 'localhost',
      port: parseInt(process.env.DATABASE_PORT) || 5432,
      username: process.env.DATABASE_USER || 'postgres',
      password: process.env.DATABASE_PASSWORD || 'password',
      database: process.env.DATABASE_NAME || 'rsglider',
      autoLoadEntities: true,
      synchronize: process.env.NODE_ENV !== 'production',
    }),
    ThrottlerModule.forRoot([{
      ttl: 60000,
      limit: 10,
    }]),
    ${moduleImports},
  ],
})
export class AppModule {}
`;
        const appModulePath = path.join(this.srcDir, 'app.module.ts');
        fs.writeFileSync(appModulePath, appModuleContent);
        console.log('  ✓ Updated app.module.ts');
    }
    getMethodNameFromOperationId(operationId) {
        return operationId.replace(/[^a-zA-Z0-9]/g, '').replace(/^./, str => str.toLowerCase());
    }
    extractPathParams(pathStr) {
        const matches = pathStr.match(/\{([^}]+)\}/g);
        return matches ? matches.map(match => match.slice(1, -1)) : [];
    }
    convertPathToNestJS(pathStr) {
        return pathStr.replace(/\{([^}]+)\}/g, ':$1').replace(/^\/[^\/]+/, '');
    }
    generateServiceCallParams(endpoint, pathParams) {
        const params = [];
        pathParams.forEach(param => {
            params.push(param);
        });
        if (endpoint.parameters?.some(p => p.in === 'query')) {
            params.push('query');
        }
        if (['POST', 'PUT', 'PATCH'].includes(endpoint.method) && endpoint.requestBody) {
            params.push('body');
        }
        return params.join(', ');
    }
    generateServiceMethodParams(endpoint, pathParams) {
        const params = [];
        pathParams.forEach(param => {
            params.push(`${param}: string`);
        });
        if (endpoint.parameters?.some(p => p.in === 'query')) {
            params.push('query?: any');
        }
        if (['POST', 'PUT', 'PATCH'].includes(endpoint.method) && endpoint.requestBody) {
            params.push('body?: any');
        }
        return params.join(', ');
    }
    capitalize(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }
    ensureDirectoryExists(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
    }
}
async function main() {
    const generator = new CompleteAPIGenerator();
    await generator.generate();
}
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=complete-generator.js.map