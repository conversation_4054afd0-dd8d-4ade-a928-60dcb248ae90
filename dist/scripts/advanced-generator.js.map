{"version": 3, "file": "advanced-generator.js", "sourceRoot": "", "sources": ["../../scripts/advanced-generator.ts"], "names": [], "mappings": ";;;AAEA,yBAAyB;AAGzB,6BAA6B;AAoBnB,mBAAmB,EAAE,CAAA;AAAE,KAAK;IAClC,OAAO,EAAA,EAAA,CAAC,GAAG,CAAC,8BAA8B,CAAC;IAE3C,IAAI,EAAA,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE;QAC9C,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAE9C,MAAM,SAAS,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC;QAC7D,MAAM,WAAW,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC;QAE5D,IAAI,OAAO,GAAG;;;WAGT,WAAW,cAAc,UAAU;CAC7C,CAAC;QAGI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAC9C,OAAO,IAAI,YAAY,UAAU,kBAAkB,QAAQ,UAAU,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,IAAI,iBAAiB,GAAG;YAClB,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,UAAU;eAC3C,UAAU;;;eAGV,SAAS;iCACS,UAAU,YAAY,WAAW;CACjE,CAAC;QAGI,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YACtC,MAAM,UAAU,GAAG,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAC3E,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YACjD,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,WAAW,GAAG,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,IAAI,EAAE,CAAC;YAE7E,iBAAiB,IAAI;KACxB,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC;8BAC9C,QAAQ,CAAC,OAAO;;UAEpC,UAAU,GAAG,CAAC;YAGhB,MAAM,MAAM,GAAa,EAAE,CAAC;YAE5B,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACzB,MAAM,CAAC,IAAI,CAAC,WAAW,KAAK,MAAM,KAAK,UAAU,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;YAEH,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACrC,CAAC;YAED,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;gBAC1E,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACnC,CAAC;YAED,iBAAiB,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,iBAAiB,IAAI;kBACX,UAAU,WAAW,UAAU,IAAI,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,UAAU,CAAC;;CAExG,CAAC;QACI,CAAC,CAAC,CAAC;QAEH,iBAAiB,IAAI,KAAK,CAAC;QAE3B,MAAM,WAAW,GAAG,OAAO,GAAG,iBAAiB,CAAC;QAChD,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,GAAG,UAAU,gBAAgB,CAAC,CAAC;QACzF,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC;QACzD,EAAE,CAAC,aAAa,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,iBAAiB,UAAU,IAAI,UAAU,gBAAgB,CAAC,CAAC;IACzE,CAAC,CAAC;CACH,CAAA;AAEO,gBAAgB,EAAE,CAAA;AAAE,KAAK;IAC/B,OAAO,EAAA,EAAA,CAAC,GAAG,CAAC,4BAA4B,CAAC;IAEzC,IAAI,EAAA,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE;QAC9C,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAE9C,MAAM,SAAS,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC;QAE1D,IAAI,cAAc,GAAG;;;;;eAKZ,SAAS;;;;CAIvB,CAAC;QAGI,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YACtC,MAAM,UAAU,GAAG,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAC3E,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEzD,cAAc,IAAI;UAChB,UAAU,IAAI,IAAI,CAAC,2BAA2B,CAAC,QAAQ,EAAE,UAAU,CAAC;yBACrD,QAAQ,CAAC,OAAO;iBACxB,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAI;;;CAGhD,CAAC;QACI,CAAC,CAAC,CAAC;QAEH,cAAc,IAAI,KAAK,CAAC;QAExB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,GAAG,UAAU,aAAa,CAAC,CAAC;QACnF,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;QACtD,EAAE,CAAC,aAAa,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,iBAAiB,UAAU,IAAI,UAAU,aAAa,CAAC,CAAC;IACtE,CAAC,CAAC;CACH,CAAA;AAEO,eAAe,EAAE,CAAA;AAAE,KAAK;IAC9B,OAAO,EAAA,EAAA,CAAC,GAAG,CAAC,0BAA0B,CAAC;IAEvC,IAAI,EAAA,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE;QAC9C,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAE9C,MAAM,SAAS,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC;QACzD,MAAM,cAAc,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC;QAClE,MAAM,WAAW,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC;QAE5D,IAAI,aAAa,GAAG;;WAEf,cAAc,cAAc,UAAU;WACtC,WAAW,cAAc,UAAU;;;;;;kBAM5B,cAAc;gBAChB,WAAW;cACb,WAAW;;eAEV,SAAS;CACvB,CAAC;QAEI,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,GAAG,UAAU,YAAY,CAAC,CAAC;QACjF,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QACrD,EAAE,CAAC,aAAa,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QAC5C,OAAO,CAAC,GAAG,CAAC,iBAAiB,UAAU,IAAI,UAAU,YAAY,CAAC,CAAC;IACrE,CAAC,CAAC;CACH,CAAA;AAEO,eAAe,EAAE,CAAA;AAAE,KAAK;IAC9B,OAAO,EAAA,EAAA,CAAC,GAAG,CAAC,8BAA8B,CAAC;IAE3C,KAAK,EAAC,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;SAClD,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;SAC5D,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;SAC7C,IAAI,CAAC,IAAI,CAAC;IAEb,KAAK,EAAC,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;SACrD,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;SAC5D,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,YAAY,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,IAAI,WAAW,CAAC;SACzF,IAAI,CAAC,IAAI,CAAC;IAEb,GAAG,EAAC,gBAAgB,GAAG;;;;EAIzB,gBAAgB;;;;;;;;;;;;;;;;;;;;;MAqBZ,aAAa;;;;CAIlB;IAEG,KAAK,EAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,eAAe,CAAC;IAC7D,EAAE,EAAA,EAAA,CAAC,aAAa,CAAC,aAAa,EAAE,gBAAgB,CAAC;IACjD,OAAO,EAAA,EAAA,CAAC,GAAG,CAAC,2BAA2B,CAAC;CACzC,CAAA;AAGO,4BAA4B,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;AAAE,MAAM,CAAA;AAAC,CAAC;IACjE,OAAO,WAAW,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;AAC1F,CAAC;AAEO,iBAAiB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;AAAE,MAAM,CAAC,CAAC,CAAA;AAAC,CAAC;IACjD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IAC3C,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACjE,CAAC;AAEO,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;AAAE,MAAM,CAAA;AAAC,CAAC;IACjD,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AACtE,CAAC;AAEO,yBAAyB,CAAC,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,CAAA;AAAE,MAAM,CAAA;AAAC,CAAC;IACvF,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACzB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,IAAI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,CAAC;QACrD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvB,CAAC;IAED,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;QAC/E,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC;AAEO,2BAA2B,CAAC,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,CAAA;AAAE,MAAM,CAAA;AAAC,CAAC;IACzF,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACzB,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,UAAU,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH,IAAI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,CAAC;QACrD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC7B,CAAC;IAED,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;QAC/E,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC5B,CAAC;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC;AAEO,UAAU,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;AAAE,MAAM,CAAA;AAAC,CAAC;IACvC,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC;AAIH,KAAK,UAAU,IAAI;IACjB,MAAM,SAAS,GAAG,IAAI,oBAAoB,EAAE,CAAC;IAC7C,MAAM,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC7B,CAAC;AAED,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC"}