#!/usr/bin/env ts-node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const fs = require("fs");
const yaml = require("js-yaml");
const path = require("path");
class ProductionAPIGeneratorV2 {
    constructor() {
        this.modules = new Map();
        this.srcDir = path.join(process.cwd(), 'src');
    }
    async generate() {
        console.log('🚀 Starting PRODUCTION-QUALITY NestJS API generation...');
        this.loadOpenAPISpec();
        this.cleanupOldGeneration();
        this.setupCleanStructure();
        this.analyzeOpenAPISpec();
        await this.generateProductionModules();
        this.generateAppModule();
        console.log('✅ Production-quality API generated successfully!');
    }
    loadOpenAPISpec() {
        try {
            const openApiPath = path.join(process.cwd(), 'api-docs', 'openapi.yaml');
            const openApiContent = fs.readFileSync(openApiPath, 'utf8');
            this.spec = yaml.load(openApiContent);
            console.log('✅ OpenAPI specification loaded');
        }
        catch (error) {
            console.error('❌ Failed to load OpenAPI specification:', error);
            process.exit(1);
        }
    }
    cleanupOldGeneration() {
        console.log('🧹 Cleaning up old generated code...');
        const moduleNames = ['auth', 'users', 'admin', 'store', 'developer', 'webhooks'];
        moduleNames.forEach(moduleName => {
            const modulePath = path.join(this.srcDir, moduleName);
            if (fs.existsSync(modulePath)) {
                const filesToRemove = [
                    `${moduleName}.controller.ts`,
                    `${moduleName}.service.ts`,
                    `${moduleName}.module.ts`
                ];
                filesToRemove.forEach(file => {
                    const filePath = path.join(modulePath, file);
                    if (fs.existsSync(filePath)) {
                        fs.unlinkSync(filePath);
                    }
                });
                const dtoPath = path.join(modulePath, 'dto');
                if (fs.existsSync(dtoPath)) {
                    fs.rmSync(dtoPath, { recursive: true });
                }
            }
        });
    }
    setupCleanStructure() {
        console.log('📁 Setting up clean production structure...');
        const moduleNames = ['auth', 'users', 'admin', 'store', 'developer', 'webhooks'];
        moduleNames.forEach(moduleName => {
            const modulePath = path.join(this.srcDir, moduleName);
            this.ensureDirectoryExists(modulePath);
            this.ensureDirectoryExists(path.join(modulePath, 'dto'));
            this.ensureDirectoryExists(path.join(modulePath, 'entities'));
            this.modules.set(moduleName, {
                name: moduleName,
                endpoints: [],
                schemas: []
            });
        });
    }
    analyzeOpenAPISpec() {
        console.log('🔍 Analyzing OpenAPI specification...');
        if (this.spec.paths) {
            Object.entries(this.spec.paths).forEach(([pathKey, pathItem]) => {
                if (!pathItem)
                    return;
                const moduleName = this.getModuleFromPath(pathKey);
                const module = this.modules.get(moduleName);
                if (module) {
                    ['get', 'post', 'put', 'patch', 'delete'].forEach(method => {
                        const operation = pathItem[method];
                        if (operation) {
                            module.endpoints.push({
                                path: pathKey,
                                method: method.toUpperCase(),
                                operationId: operation.operationId || `${method}${pathKey.replace(/[^a-zA-Z0-9]/g, '')}`,
                                summary: operation.summary || `${method.toUpperCase()} ${pathKey}`,
                                requestBody: this.extractRequestBodySchema(operation),
                                responses: this.extractResponseSchemas(operation),
                                parameters: operation.parameters || []
                            });
                        }
                    });
                }
            });
        }
        if (this.spec.components?.schemas) {
            Object.keys(this.spec.components.schemas).forEach(schemaName => {
                const moduleName = this.getModuleFromSchemaName(schemaName);
                const module = this.modules.get(moduleName);
                if (module) {
                    module.schemas.push(schemaName);
                }
            });
        }
    }
    getModuleFromPath(pathKey) {
        if (pathKey.startsWith('/auth'))
            return 'auth';
        if (pathKey.startsWith('/users'))
            return 'users';
        if (pathKey.startsWith('/admin'))
            return 'admin';
        if (pathKey.startsWith('/store'))
            return 'store';
        if (pathKey.startsWith('/developer'))
            return 'developer';
        if (pathKey.startsWith('/webhooks'))
            return 'webhooks';
        return 'users';
    }
    getModuleFromSchemaName(schemaName) {
        const lower = schemaName.toLowerCase();
        if (lower.includes('auth') || lower.includes('login') || lower.includes('register') || lower.includes('token'))
            return 'auth';
        if (lower.includes('admin'))
            return 'admin';
        if (lower.includes('store') || lower.includes('cart') || lower.includes('pricing') || lower.includes('subscription'))
            return 'store';
        if (lower.includes('developer') || lower.includes('repository') || lower.includes('payout') || lower.includes('gitea'))
            return 'developer';
        if (lower.includes('webhook'))
            return 'webhooks';
        if (lower.includes('user'))
            return 'users';
        return 'users';
    }
    extractRequestBodySchema(operation) {
        if (!operation.requestBody)
            return undefined;
        const requestBody = operation.requestBody;
        const content = requestBody.content?.['application/json'];
        if (!content?.schema)
            return undefined;
        return content.schema;
    }
    extractResponseSchemas(operation) {
        const schemas = {};
        if (operation.responses) {
            Object.entries(operation.responses).forEach(([statusCode, response]) => {
                const responseObj = response;
                const content = responseObj.content?.['application/json'];
                if (content?.schema) {
                    schemas[statusCode] = content.schema;
                }
            });
        }
        return schemas;
    }
    async generateProductionModules() {
        console.log('🏭 Generating production-quality modules...');
        for (const [moduleName, moduleInfo] of this.modules) {
            if (moduleInfo.endpoints.length === 0 && moduleInfo.schemas.length === 0)
                continue;
            console.log(`  ✓ Generating ${moduleName} module...`);
            await this.generateCleanDTOs(moduleInfo);
            await this.generateSmartController(moduleInfo);
            await this.generateSmartService(moduleInfo);
            await this.generateCleanModule(moduleInfo);
        }
    }
    async generateCleanDTOs(moduleInfo) {
        if (!this.spec.components?.schemas)
            return;
        moduleInfo.schemas.forEach(schemaName => {
            const schema = this.spec.components.schemas[schemaName];
            const dtoContent = this.generateCleanDTO(schemaName, schema);
            const fileName = this.toKebabCase(schemaName) + '.dto.ts';
            const dtoPath = path.join(this.srcDir, moduleInfo.name, 'dto', fileName);
            fs.writeFileSync(dtoPath, dtoContent);
        });
    }
    generateCleanDTO(schemaName, schema) {
        const imports = new Set();
        imports.add("import { ApiProperty } from '@nestjs/swagger';");
        imports.add("import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsEnum, IsArray, IsUUID, MinLength, MaxLength, Min, Max } from 'class-validator';");
        let classContent = `export class ${schemaName} {\n`;
        if (schema.properties) {
            Object.entries(schema.properties).forEach(([propName, propSchema]) => {
                const property = propSchema;
                const isRequired = schema.required?.includes(propName) || false;
                classContent += this.generateCleanProperty(propName, property, isRequired);
            });
        }
        classContent += '}\n';
        return Array.from(imports).join('\n') + '\n\n' + classContent;
    }
    generateCleanProperty(propName, property, isRequired) {
        let decorators = '';
        const apiPropertyOptions = [];
        if (property.description) {
            apiPropertyOptions.push(`description: '${property.description.replace(/'/g, "\\'")}'`);
        }
        if (!isRequired) {
            apiPropertyOptions.push('required: false');
        }
        decorators += `  @ApiProperty(${apiPropertyOptions.length > 0 ? `{ ${apiPropertyOptions.join(', ')} }` : ''})\n`;
        if (!isRequired) {
            decorators += '  @IsOptional()\n';
        }
        else {
            decorators += '  @IsNotEmpty()\n';
        }
        if (property.type === 'string') {
            decorators += '  @IsString()\n';
            if (property.format === 'email') {
                decorators += '  @IsEmail()\n';
            }
        }
        else if (property.type === 'number' || property.type === 'integer') {
            decorators += '  @IsNumber()\n';
        }
        else if (property.type === 'boolean') {
            decorators += '  @IsBoolean()\n';
        }
        else if (property.type === 'array') {
            decorators += '  @IsArray()\n';
        }
        let typeAnnotation = this.getTypeScriptType(property);
        const optionalMarker = isRequired ? '' : '?';
        return `${decorators}  ${propName}${optionalMarker}: ${typeAnnotation};\n\n`;
    }
    getTypeScriptType(schema) {
        switch (schema.type) {
            case 'string': return 'string';
            case 'number':
            case 'integer': return 'number';
            case 'boolean': return 'boolean';
            case 'array':
                if (schema.items) {
                    const itemType = this.getTypeScriptType(schema.items);
                    return `${itemType}[]`;
                }
                return 'any[]';
            case 'object': return 'object';
            default: return 'any';
        }
    }
    toKebabCase(str) {
        return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
    }
    capitalize(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }
    ensureDirectoryExists(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
    }
    async generateSmartController(moduleInfo) {
        const className = `${this.capitalize(moduleInfo.name)}Controller`;
        const serviceName = `${this.capitalize(moduleInfo.name)}Service`;
        let imports = `import { Controller, Get, Post, Put, Patch, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { ${serviceName} } from './${moduleInfo.name}.service';
`;
        moduleInfo.schemas.forEach(schemaName => {
            const fileName = this.toKebabCase(schemaName);
            imports += `import { ${schemaName} } from './dto/${fileName}.dto';\n`;
        });
        let controllerContent = `
@ApiTags('${moduleInfo.name}')
@Controller('${moduleInfo.name}')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ${className} {
  constructor(private readonly ${moduleInfo.name}Service: ${serviceName}) {}
`;
        moduleInfo.endpoints.forEach(endpoint => {
            const methodName = this.getMethodNameFromOperationId(endpoint.operationId);
            const httpMethod = endpoint.method.toLowerCase();
            const pathParams = this.extractPathParams(endpoint.path);
            const queryParams = endpoint.parameters?.filter(p => p.in === 'query') || [];
            controllerContent += `
  @${this.capitalize(httpMethod)}('${this.convertPathToNestJS(endpoint.path)}')
  @ApiOperation({ summary: '${endpoint.summary}' })
  @ApiResponse({ status: 200, description: 'Success' })
  async ${methodName}(`;
            const params = [];
            pathParams.forEach(param => {
                params.push(`@Param('${param}') ${param}: string`);
            });
            if (queryParams.length > 0) {
                params.push(`@Query() query: any`);
            }
            if (['post', 'put', 'patch'].includes(httpMethod) && endpoint.requestBody) {
                params.push(`@Body() body: any`);
            }
            controllerContent += params.join(', ');
            controllerContent += `) {
    return this.${moduleInfo.name}Service.${methodName}(${this.generateServiceCallParams(endpoint, pathParams)});
  }
`;
        });
        controllerContent += '}\n';
        const fullContent = imports + controllerContent;
        const controllerPath = path.join(this.srcDir, moduleInfo.name, `${moduleInfo.name}.controller.ts`);
        fs.writeFileSync(controllerPath, fullContent);
    }
    async generateSmartService(moduleInfo) {
        const className = `${this.capitalize(moduleInfo.name)}Service`;
        let serviceContent = `import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class ${className} {
  constructor(
    // Add repository injections here when entities are generated
  ) {}
`;
        moduleInfo.endpoints.forEach(endpoint => {
            const methodName = this.getMethodNameFromOperationId(endpoint.operationId);
            const pathParams = this.extractPathParams(endpoint.path);
            serviceContent += `
  async ${methodName}(${this.generateServiceMethodParams(endpoint, pathParams)}): Promise<any> {
    // TODO: Implement ${endpoint.summary}
    // Method: ${endpoint.method} ${endpoint.path}
    return {};
  }
`;
        });
        serviceContent += '}\n';
        const servicePath = path.join(this.srcDir, moduleInfo.name, `${moduleInfo.name}.service.ts`);
        fs.writeFileSync(servicePath, serviceContent);
    }
    async generateCleanModule(moduleInfo) {
        const className = `${this.capitalize(moduleInfo.name)}Module`;
        const controllerName = `${this.capitalize(moduleInfo.name)}Controller`;
        const serviceName = `${this.capitalize(moduleInfo.name)}Service`;
        let moduleContent = `import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ${controllerName} } from './${moduleInfo.name}.controller';
import { ${serviceName} } from './${moduleInfo.name}.service';

@Module({
  imports: [
    // TypeOrmModule.forFeature([]), // Add entities here
  ],
  controllers: [${controllerName}],
  providers: [${serviceName}],
  exports: [${serviceName}],
})
export class ${className} {}
`;
        const modulePath = path.join(this.srcDir, moduleInfo.name, `${moduleInfo.name}.module.ts`);
        fs.writeFileSync(modulePath, moduleContent);
    }
    generateAppModule() {
        const moduleImports = Array.from(this.modules.keys())
            .filter(name => this.modules.get(name).endpoints.length > 0 || this.modules.get(name).schemas.length > 0)
            .map(name => `${this.capitalize(name)}Module`)
            .join(', ');
        const importStatements = Array.from(this.modules.keys())
            .filter(name => this.modules.get(name).endpoints.length > 0 || this.modules.get(name).schemas.length > 0)
            .map(name => `import { ${this.capitalize(name)}Module } from '@/${name}/${name}.module';`)
            .join('\n');
        let appModuleContent = `import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ThrottlerModule } from '@nestjs/throttler';
${importStatements}

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT) || 5432,
      username: process.env.DB_USERNAME || 'postgres',
      password: process.env.DB_PASSWORD || 'password',
      database: process.env.DB_NAME || 'rsglider',
      autoLoadEntities: true,
      synchronize: process.env.NODE_ENV !== 'production',
    }),
    ThrottlerModule.forRoot([{
      ttl: 60000,
      limit: 10,
    }]),
    ${moduleImports},
  ],
})
export class AppModule {}
`;
        const appModulePath = path.join(this.srcDir, 'app.module.ts');
        fs.writeFileSync(appModulePath, appModuleContent);
    }
    getMethodNameFromOperationId(operationId) {
        return operationId.replace(/[^a-zA-Z0-9]/g, '').replace(/^./, str => str.toLowerCase());
    }
    extractPathParams(path) {
        const matches = path.match(/\{([^}]+)\}/g);
        return matches ? matches.map(match => match.slice(1, -1)) : [];
    }
    convertPathToNestJS(path) {
        return path.replace(/\{([^}]+)\}/g, ':$1').replace(/^\/[^\/]+/, '');
    }
    generateServiceCallParams(endpoint, pathParams) {
        const params = [];
        pathParams.forEach(param => {
            params.push(param);
        });
        if (endpoint.parameters?.some(p => p.in === 'query')) {
            params.push('query');
        }
        if (['POST', 'PUT', 'PATCH'].includes(endpoint.method) && endpoint.requestBody) {
            params.push('body');
        }
        return params.join(', ');
    }
    generateServiceMethodParams(endpoint, pathParams) {
        const params = [];
        pathParams.forEach(param => {
            params.push(`${param}: string`);
        });
        if (endpoint.parameters?.some(p => p.in === 'query')) {
            params.push('query?: any');
        }
        if (['POST', 'PUT', 'PATCH'].includes(endpoint.method) && endpoint.requestBody) {
            params.push('body?: any');
        }
        return params.join(', ');
    }
}
async function main() {
    const generator = new ProductionAPIGeneratorV2();
    await generator.generate();
}
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=generate-production-api-v2.js.map