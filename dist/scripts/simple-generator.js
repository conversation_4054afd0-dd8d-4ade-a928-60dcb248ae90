#!/usr/bin/env ts-node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const fs = require("fs");
const yaml = require("js-yaml");
const path = require("path");
class SimpleAPIGenerator {
    constructor() {
        this.srcDir = path.join(process.cwd(), 'src');
    }
    async generate() {
        console.log('🚀 Generating clean NestJS API with aliased imports...');
        const openApiPath = path.join(process.cwd(), 'api-docs', 'openapi.yaml');
        const openApiContent = fs.readFileSync(openApiPath, 'utf8');
        this.spec = yaml.load(openApiContent);
        this.cleanupBrokenFiles();
        this.generateCleanDTOs();
        this.fixMainTsImports();
        console.log('✅ Clean API generated successfully!');
    }
    cleanupBrokenFiles() {
        console.log('🧹 Cleaning up broken generated files...');
        const moduleNames = ['auth', 'users', 'admin', 'store', 'developer', 'webhooks'];
        moduleNames.forEach(moduleName => {
            const modulePath = path.join(this.srcDir, moduleName);
            if (fs.existsSync(modulePath)) {
                const filesToRemove = [
                    `${moduleName}.controller.ts`,
                    `${moduleName}.service.ts`,
                    `${moduleName}.module.ts`
                ];
                filesToRemove.forEach(file => {
                    const filePath = path.join(modulePath, file);
                    if (fs.existsSync(filePath)) {
                        fs.unlinkSync(filePath);
                        console.log(`  ✓ Removed broken ${file}`);
                    }
                });
            }
        });
    }
    generateCleanDTOs() {
        console.log('📝 Generating clean DTOs with proper names...');
        if (!this.spec.components?.schemas)
            return;
        Object.entries(this.spec.components.schemas).forEach(([schemaName, schema]) => {
            const schemaObj = schema;
            const moduleName = this.getModuleFromSchemaName(schemaName);
            const modulePath = path.join(this.srcDir, moduleName);
            this.ensureDirectoryExists(modulePath);
            this.ensureDirectoryExists(path.join(modulePath, 'dto'));
            const dtoContent = this.generateDTO(schemaName, schemaObj);
            const fileName = this.toProperKebabCase(schemaName) + '.dto.ts';
            const dtoPath = path.join(modulePath, 'dto', fileName);
            fs.writeFileSync(dtoPath, dtoContent);
            console.log(`  ✓ Generated ${moduleName}/${fileName}`);
        });
    }
    generateDTO(schemaName, schema) {
        let content = `import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class ${schemaName} {
`;
        if (schema.properties) {
            Object.entries(schema.properties).forEach(([propName, propSchema]) => {
                const property = propSchema;
                const isRequired = schema.required?.includes(propName) || false;
                content += `  @ApiProperty(${!isRequired ? '{ required: false }' : ''})\n`;
                if (!isRequired) {
                    content += `  @IsOptional()\n`;
                }
                else {
                    content += `  @IsNotEmpty()\n`;
                }
                if (property.type === 'string') {
                    content += `  @IsString()\n`;
                    if (property.format === 'email') {
                        content += `  @IsEmail()\n`;
                    }
                }
                else if (property.type === 'number' || property.type === 'integer') {
                    content += `  @IsNumber()\n`;
                }
                else if (property.type === 'boolean') {
                    content += `  @IsBoolean()\n`;
                }
                else if (property.type === 'array') {
                    content += `  @IsArray()\n`;
                }
                const tsType = this.getTypeScriptType(property);
                const optional = isRequired ? '' : '?';
                content += `  ${propName}${optional}: ${tsType};\n\n`;
            });
        }
        content += '}\n';
        return content;
    }
    getTypeScriptType(schema) {
        switch (schema.type) {
            case 'string': return 'string';
            case 'number':
            case 'integer': return 'number';
            case 'boolean': return 'boolean';
            case 'array':
                if (schema.items) {
                    const itemType = this.getTypeScriptType(schema.items);
                    return `${itemType}[]`;
                }
                return 'any[]';
            case 'object': return 'object';
            default: return 'any';
        }
    }
    getModuleFromSchemaName(schemaName) {
        const lower = schemaName.toLowerCase();
        if (lower.includes('auth') || lower.includes('login') || lower.includes('register') || lower.includes('token'))
            return 'auth';
        if (lower.includes('admin'))
            return 'admin';
        if (lower.includes('store') || lower.includes('cart') || lower.includes('pricing') || lower.includes('subscription'))
            return 'store';
        if (lower.includes('developer') || lower.includes('repository') || lower.includes('payout') || lower.includes('gitea'))
            return 'developer';
        if (lower.includes('webhook'))
            return 'webhooks';
        if (lower.includes('user'))
            return 'users';
        return 'users';
    }
    toProperKebabCase(str) {
        return str
            .replace(/([a-z0-9])([A-Z])/g, '$1-$2')
            .toLowerCase()
            .replace(/^-/, '');
    }
    fixMainTsImports() {
        console.log('🔧 Fixing main.ts to use aliased imports...');
        const mainTsPath = path.join(this.srcDir, 'main.ts');
        if (!fs.existsSync(mainTsPath))
            return;
        let content = fs.readFileSync(mainTsPath, 'utf8');
        content = content.replace(/from '\.\.\/common\//g, "from '@/common/");
        content = content.replace(/from '\.\/common\//g, "from '@/common/");
        content = content.replace(/from '\.\.\/config\//g, "from '@/config/");
        content = content.replace(/from '\.\/config\//g, "from '@/config/");
        fs.writeFileSync(mainTsPath, content);
        console.log('  ✓ Fixed main.ts imports');
    }
    ensureDirectoryExists(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
    }
}
async function main() {
    const generator = new SimpleAPIGenerator();
    await generator.generate();
}
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=simple-generator.js.map