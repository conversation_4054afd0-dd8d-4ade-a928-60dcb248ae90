#!/usr/bin/env ts-node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NestJSCodeGenerator = void 0;
const fs = require("fs");
const yaml = require("js-yaml");
const path = require("path");
class NestJSCodeGenerator {
    constructor(config) {
        this.config = config;
        this.loadOpenAPISpec();
    }
    loadOpenAPISpec() {
        try {
            const specContent = fs.readFileSync(this.config.openApiPath, 'utf8');
            this.spec = yaml.load(specContent);
            console.log('✅ OpenAPI specification loaded successfully');
        }
        catch (error) {
            console.error('❌ Failed to load OpenAPI specification:', error);
            process.exit(1);
        }
    }
    async generateAll() {
        console.log('🚀 Starting NestJS code generation...');
        await this.generateDTOs();
        await this.generateEntities();
        await this.generateControllers();
        await this.generateServices();
        await this.generateModules();
        console.log('✅ Code generation completed successfully!');
    }
    async generateDTOs() {
        console.log('📝 Generating DTOs...');
        if (!this.spec.components?.schemas) {
            console.log('⚠️  No schemas found in OpenAPI spec');
            return;
        }
        for (const [schemaName, schema] of Object.entries(this.spec.components.schemas)) {
            if (this.isReferenceObject(schema))
                continue;
            const dtoContent = this.generateDTOClass(schemaName, schema);
            const fileName = this.toCamelCase(schemaName);
            const filePath = path.join(this.config.outputDir, 'dto', `${fileName}.dto.ts`);
            this.ensureDirectoryExists(path.dirname(filePath));
            fs.writeFileSync(filePath, dtoContent);
            console.log(`  ✓ Generated ${fileName}.dto.ts`);
        }
    }
    generateDTOClass(name, schema) {
        const className = this.toPascalCase(name);
        const imports = new Set();
        imports.add("import { ApiProperty } from '@nestjs/swagger';");
        const validationImports = this.getValidationImports(schema);
        if (validationImports.length > 0) {
            imports.add(`import { ${validationImports.join(', ')} } from 'class-validator';`);
        }
        let classContent = `export class ${className} {\n`;
        if (schema.properties) {
            for (const [propName, propSchema] of Object.entries(schema.properties)) {
                if (this.isReferenceObject(propSchema))
                    continue;
                const property = this.generateProperty(propName, propSchema, schema.required?.includes(propName) || false);
                classContent += property;
            }
        }
        classContent += '}\n';
        return Array.from(imports).join('\n') + '\n\n' + classContent;
    }
    generateProperty(name, schema, isRequired) {
        const decorators = this.generatePropertyDecorators(name, schema, isRequired);
        const type = this.getTypeScriptType(schema);
        const optional = isRequired ? '' : '?';
        return `${decorators}  ${name}${optional}: ${type};\n\n`;
    }
    generatePropertyDecorators(name, schema, isRequired) {
        let decorators = '';
        const apiPropertyOptions = [];
        if (schema.description) {
            apiPropertyOptions.push(`description: '${schema.description.replace(/'/g, "\\'")}'`);
        }
        if (schema.example !== undefined) {
            apiPropertyOptions.push(`example: ${JSON.stringify(schema.example)}`);
        }
        if (schema.enum) {
            apiPropertyOptions.push(`enum: [${schema.enum.map(v => `'${v}'`).join(', ')}]`);
        }
        if (schema.type === 'array' && schema.items && !this.isReferenceObject(schema.items)) {
            if (schema.items.type === 'string') {
                apiPropertyOptions.push(`type: [String]`);
            }
            else if (schema.items.type === 'number' || schema.items.type === 'integer') {
                apiPropertyOptions.push(`type: [Number]`);
            }
            else if (schema.items.type === 'object') {
                apiPropertyOptions.push(`type: [Object]`);
            }
            else {
                const itemType = this.getTypeScriptType(schema.items);
                apiPropertyOptions.push(`type: [${itemType}]`);
            }
        }
        if (!isRequired) {
            apiPropertyOptions.push('required: false');
        }
        decorators += `  @ApiProperty(${apiPropertyOptions.length > 0 ? `{ ${apiPropertyOptions.join(', ')} }` : ''})\n`;
        const validationDecorators = this.generateValidationDecorators(schema, isRequired);
        decorators += validationDecorators;
        return decorators;
    }
    generateValidationDecorators(schema, isRequired) {
        let decorators = '';
        if (isRequired) {
            decorators += '  @IsNotEmpty()\n';
        }
        else {
            decorators += '  @IsOptional()\n';
        }
        switch (schema.type) {
            case 'string':
                decorators += '  @IsString()\n';
                if (schema.format === 'email') {
                    decorators += '  @IsEmail()\n';
                }
                if (schema.minLength) {
                    decorators += `  @MinLength(${schema.minLength})\n`;
                }
                if (schema.maxLength) {
                    decorators += `  @MaxLength(${schema.maxLength})\n`;
                }
                break;
            case 'number':
            case 'integer':
                decorators += '  @IsNumber()\n';
                if (schema.minimum) {
                    decorators += `  @Min(${schema.minimum})\n`;
                }
                if (schema.maximum) {
                    decorators += `  @Max(${schema.maximum})\n`;
                }
                break;
            case 'boolean':
                decorators += '  @IsBoolean()\n';
                break;
            case 'array':
                decorators += '  @IsArray()\n';
                break;
        }
        if (schema.enum) {
            decorators += `  @IsEnum([${schema.enum.map(v => `'${v}'`).join(', ')}])\n`;
        }
        return decorators;
    }
    getValidationImports(schema) {
        const imports = new Set();
        const addValidationImports = (s) => {
            imports.add('IsOptional');
            imports.add('IsNotEmpty');
            switch (s.type) {
                case 'string':
                    imports.add('IsString');
                    if (s.format === 'email')
                        imports.add('IsEmail');
                    if (s.minLength)
                        imports.add('MinLength');
                    if (s.maxLength)
                        imports.add('MaxLength');
                    break;
                case 'number':
                case 'integer':
                    imports.add('IsNumber');
                    if (s.minimum)
                        imports.add('Min');
                    if (s.maximum)
                        imports.add('Max');
                    break;
                case 'boolean':
                    imports.add('IsBoolean');
                    break;
                case 'array':
                    imports.add('IsArray');
                    break;
            }
            if (s.enum)
                imports.add('IsEnum');
        };
        addValidationImports(schema);
        if (schema.properties) {
            Object.values(schema.properties).forEach(prop => {
                if (!this.isReferenceObject(prop)) {
                    addValidationImports(prop);
                }
            });
        }
        return Array.from(imports);
    }
    getTypeScriptType(schema) {
        switch (schema.type) {
            case 'string':
                return schema.enum ? schema.enum.map(v => `'${v}'`).join(' | ') : 'string';
            case 'number':
            case 'integer':
                return 'number';
            case 'boolean':
                return 'boolean';
            case 'array':
                if (schema.items && !this.isReferenceObject(schema.items)) {
                    return `${this.getTypeScriptType(schema.items)}[]`;
                }
                return 'any[]';
            case 'object':
                return 'object';
            default:
                return 'any';
        }
    }
    async generateEntities() {
        console.log('🗄️  Generating entities...');
    }
    async generateControllers() {
        console.log('🎮 Generating controllers...');
    }
    async generateServices() {
        console.log('⚙️  Generating services...');
    }
    async generateModules() {
        console.log('📦 Generating modules...');
    }
    isReferenceObject(obj) {
        return obj && typeof obj === 'object' && '$ref' in obj;
    }
    toCamelCase(str) {
        return str.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
            return index === 0 ? word.toLowerCase() : word.toUpperCase();
        }).replace(/\s+/g, '');
    }
    toPascalCase(str) {
        return str.replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => {
            return word.toUpperCase();
        }).replace(/\s+/g, '');
    }
    ensureDirectoryExists(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
    }
}
exports.NestJSCodeGenerator = NestJSCodeGenerator;
async function main() {
    const config = {
        openApiPath: path.join(process.cwd(), 'api-docs', 'openapi.yaml'),
        outputDir: path.join(process.cwd(), 'src', 'generated'),
        templatesDir: path.join(process.cwd(), 'scripts', 'templates'),
    };
    const generator = new NestJSCodeGenerator(config);
    await generator.generateAll();
}
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=generate-nestjs-code.js.map