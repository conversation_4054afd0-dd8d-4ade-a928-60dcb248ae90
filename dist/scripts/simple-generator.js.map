{"version": 3, "file": "simple-generator.js", "sourceRoot": "", "sources": ["../../scripts/simple-generator.ts"], "names": [], "mappings": ";;;AAEA,yBAAyB;AACzB,gCAAgC;AAEhC,6BAA6B;AAE7B,MAAM,kBAAkB;IAItB;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;QAGtE,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;QACzE,MAAM,cAAc,GAAG,EAAE,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAC5D,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAuB,CAAC;QAG5D,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAG1B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAGzB,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAGhC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IACrD,CAAC;IAEO,kBAAkB;QACxB,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QAGxD,MAAM,WAAW,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;QAEjF,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YACtD,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBAE9B,MAAM,aAAa,GAAG;oBACpB,GAAG,UAAU,gBAAgB;oBAC7B,GAAG,UAAU,aAAa;oBAC1B,GAAG,UAAU,YAAY;iBAC1B,CAAC;gBAEF,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;oBAC7C,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC5B,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;wBACxB,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,EAAE,CAAC,CAAC;oBAC5C,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB;QACvB,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAE7D,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO;YAAE,OAAO;QAE3C,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,EAAE;YAC5E,MAAM,SAAS,GAAG,MAAgC,CAAC;YACnD,MAAM,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YAGtD,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;YACvC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;YAGzD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;YAChE,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YAEvD,EAAE,CAAC,aAAa,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,iBAAiB,UAAU,IAAI,QAAQ,EAAE,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,WAAW,CAAC,UAAkB,EAAE,MAA8B;QACpE,IAAI,OAAO,GAAG;;;eAGH,UAAU;CACxB,CAAC;QAEE,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,EAAE;gBACnE,MAAM,QAAQ,GAAG,UAAoC,CAAC;gBACtD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC;gBAGhE,OAAO,IAAI,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;gBAE3E,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,OAAO,IAAI,mBAAmB,CAAC;gBACjC,CAAC;qBAAM,CAAC;oBACN,OAAO,IAAI,mBAAmB,CAAC;gBACjC,CAAC;gBAGD,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC/B,OAAO,IAAI,iBAAiB,CAAC;oBAC7B,IAAI,QAAQ,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;wBAChC,OAAO,IAAI,gBAAgB,CAAC;oBAC9B,CAAC;gBACH,CAAC;qBAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBACrE,OAAO,IAAI,iBAAiB,CAAC;gBAC/B,CAAC;qBAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBACvC,OAAO,IAAI,kBAAkB,CAAC;gBAChC,CAAC;qBAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBACrC,OAAO,IAAI,gBAAgB,CAAC;gBAC9B,CAAC;gBAGD,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;gBAChD,MAAM,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;gBACvC,OAAO,IAAI,KAAK,QAAQ,GAAG,QAAQ,KAAK,MAAM,OAAO,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,KAAK,CAAC;QACjB,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,iBAAiB,CAAC,MAA8B;QACtD,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YACpB,KAAK,QAAQ,CAAC,CAAC,OAAO,QAAQ,CAAC;YAC/B,KAAK,QAAQ,CAAC;YACd,KAAK,SAAS,CAAC,CAAC,OAAO,QAAQ,CAAC;YAChC,KAAK,SAAS,CAAC,CAAC,OAAO,SAAS,CAAC;YACjC,KAAK,OAAO;gBACV,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;oBACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAA+B,CAAC,CAAC;oBAChF,OAAO,GAAG,QAAQ,IAAI,CAAC;gBACzB,CAAC;gBACD,OAAO,OAAO,CAAC;YACjB,KAAK,QAAQ,CAAC,CAAC,OAAO,QAAQ,CAAC;YAC/B,OAAO,CAAC,CAAC,OAAO,KAAK,CAAC;QACxB,CAAC;IACH,CAAC;IAEO,uBAAuB,CAAC,UAAkB;QAChD,MAAM,KAAK,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,MAAM,CAAC;QAC9H,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,OAAO,CAAC;QAC5C,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC;YAAE,OAAO,OAAO,CAAC;QACrI,IAAI,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,WAAW,CAAC;QAC3I,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,UAAU,CAAC;QACjD,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,OAAO,CAAC;QAC3C,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,iBAAiB,CAAC,GAAW;QAEnC,OAAO,GAAG;aACP,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC;aACtC,WAAW,EAAE;aACb,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IACvB,CAAC;IAEO,gBAAgB;QACtB,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QACrD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;YAAE,OAAO;QAEvC,IAAI,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAGlD,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,uBAAuB,EAAE,iBAAiB,CAAC,CAAC;QACtE,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,qBAAqB,EAAE,iBAAiB,CAAC,CAAC;QACpE,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,uBAAuB,EAAE,iBAAiB,CAAC,CAAC;QACtE,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,qBAAqB,EAAE,iBAAiB,CAAC,CAAC;QAEpE,EAAE,CAAC,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;IAC3C,CAAC;IAEO,qBAAqB,CAAC,OAAe;QAC3C,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;CACF;AAGD,KAAK,UAAU,IAAI;IACjB,MAAM,SAAS,GAAG,IAAI,kBAAkB,EAAE,CAAC;IAC3C,MAAM,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC7B,CAAC;AAED,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC"}