"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const core_1 = require("@nestjs/core");
const swagger_1 = require("@nestjs/swagger");
const connect_redis_1 = require("connect-redis");
const session = require("express-session");
const fs_1 = require("fs");
const passport = require("passport");
const path_1 = require("path");
const redis_1 = require("redis");
const app_module_1 = require("./app.module");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const configService = app.get(config_1.ConfigService);
    const logger = new common_1.Logger('Bootstrap');
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
            enableImplicitConversion: true,
        },
    }));
    app.enableCors({
        origin: configService.get('CORS_ORIGIN', '*').split(','),
        credentials: configService.get('CORS_CREDENTIALS', 'true') === 'true',
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: [
            'Origin',
            'X-Requested-With',
            'Content-Type',
            'Accept',
            'Authorization',
            'X-API-Key',
        ],
    });
    const redisClient = (0, redis_1.createClient)({
        url: `redis://:${configService.get('REDIS_PASSWORD')}@${configService.get('REDIS_HOST', 'redis')}:${configService.get('REDIS_PORT', 6379)}`,
    });
    await redisClient.connect();
    logger.log('Connected to Redis');
    app.use(session({
        store: new ((0, connect_redis_1.default)(session))({
            client: redisClient,
            prefix: 'rsglider:',
        }),
        secret: configService.get('SESSION_SECRET', 'dev-secret'),
        resave: false,
        saveUninitialized: false,
        cookie: {
            maxAge: parseInt(configService.get('SESSION_MAX_AGE', '86400000')),
            httpOnly: true,
            secure: configService.get('NODE_ENV') === 'production',
        },
    }));
    app.use(passport.initialize());
    app.use(passport.session());
    let openApiSpec;
    try {
        const openApiPath = (0, path_1.join)(process.cwd(), 'api-docs', 'openapi.yaml');
        const openApiContent = (0, fs_1.readFileSync)(openApiPath, 'utf8');
        openApiSpec = openApiContent;
    }
    catch (error) {
        logger.warn('Could not load OpenAPI spec, using default configuration');
    }
    const config = new swagger_1.DocumentBuilder()
        .setTitle('RSGlider API')
        .setDescription('The RSGlider API for desktop and web applications')
        .setVersion('1.0.0')
        .addBearerAuth({
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
    }, 'JWT-auth')
        .addApiKey({
        type: 'apiKey',
        name: 'X-API-Key',
        in: 'header',
        description: 'API Key for service-to-service communication',
    }, 'API-Key')
        .addTag('Authentication', 'User authentication and session management')
        .addTag('User Profile', 'User profile and account management')
        .addTag('Admin', 'Administrative operations')
        .addTag('Store', 'Marketplace and store operations')
        .addTag('Developer', 'Developer tools and repository management')
        .addTag('Webhooks', 'Webhook endpoints for external integrations')
        .build();
    const documentFactory = () => swagger_1.SwaggerModule.createDocument(app, config, {
        operationIdFactory: (_controllerKey, methodKey) => methodKey,
        deepScanRoutes: true,
    });
    swagger_1.SwaggerModule.setup('api/docs', app, documentFactory, {
        customSiteTitle: 'RSGlider API Documentation',
        customfavIcon: '/favicon.ico',
        customCss: '.swagger-ui .topbar { display: none }',
        swaggerOptions: {
            persistAuthorization: true,
            displayRequestDuration: true,
        },
    });
    app.setGlobalPrefix('api', {
        exclude: ['/health', '/'],
    });
    app.use('/health', (_req, res) => {
        res.status(200).json({
            status: 'ok',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            environment: configService.get('NODE_ENV'),
        });
    });
    const port = configService.get('PORT', 3000);
    await app.listen(port);
    logger.log(`🚀 Application is running on: http://localhost:${port}`);
    logger.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
    logger.log(`🏥 Health Check: http://localhost:${port}/health`);
}
bootstrap().catch((error) => {
    console.error('Failed to start application:', error);
    process.exit(1);
});
//# sourceMappingURL=main.js.map