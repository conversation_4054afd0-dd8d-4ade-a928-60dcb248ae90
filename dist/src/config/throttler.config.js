"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.throttlerConfig = void 0;
const config_1 = require("@nestjs/config");
exports.throttlerConfig = (0, config_1.registerAs)('throttler', () => ({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 900000,
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS, 10) || 100,
}));
//# sourceMappingURL=throttler.config.js.map