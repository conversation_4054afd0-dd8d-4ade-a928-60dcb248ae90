"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseConfig = void 0;
const config_1 = require("@nestjs/config");
exports.databaseConfig = (0, config_1.registerAs)('database', () => ({
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT, 10) || 5432,
    username: process.env.DATABASE_USER || 'rsglider',
    password: process.env.DATABASE_PASSWORD || 'rsglider_dev_password',
    database: process.env.DATABASE_NAME || 'rsglider',
    synchronize: process.env.NODE_ENV === 'development',
    logging: process.env.DEBUG_SQL === 'true',
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
}));
//# sourceMappingURL=database.config.js.map