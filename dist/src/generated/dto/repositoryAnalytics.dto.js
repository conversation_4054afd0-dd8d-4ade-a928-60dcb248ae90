"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RepositoryAnalytics = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class RepositoryAnalytics {
    static _OPENAPI_METADATA_FACTORY() {
        return { repositoryId: { required: false, type: () => String }, period: { required: false, type: () => String }, revenue: { required: false, type: () => Number }, downloads: { required: false, type: () => Number }, views: { required: false, type: () => Number }, uniqueUsers: { required: false, type: () => Number }, conversionRate: { required: false, type: () => Number }, rating: { required: false, type: () => Number }, reviewCount: { required: false, type: () => Number }, downloadsByPeriod: { required: false, type: () => [Object] }, geographicBreakdown: { required: false, type: () => Object }, referralSources: { required: false, type: () => Object } };
    }
}
exports.RepositoryAnalytics = RepositoryAnalytics;
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RepositoryAnalytics.prototype, "repositoryId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RepositoryAnalytics.prototype, "period", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RepositoryAnalytics.prototype, "revenue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RepositoryAnalytics.prototype, "downloads", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RepositoryAnalytics.prototype, "views", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RepositoryAnalytics.prototype, "uniqueUsers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Views to downloads conversion rate', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RepositoryAnalytics.prototype, "conversionRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RepositoryAnalytics.prototype, "rating", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RepositoryAnalytics.prototype, "reviewCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [Object], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], RepositoryAnalytics.prototype, "downloadsByPeriod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], RepositoryAnalytics.prototype, "geographicBreakdown", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], RepositoryAnalytics.prototype, "referralSources", void 0);
//# sourceMappingURL=repositoryAnalytics.dto.js.map