"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwoFactorEnabledResponse = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class TwoFactorEnabledResponse {
    static _OPENAPI_METADATA_FACTORY() {
        return { enabled: { required: false, type: () => Boolean }, backupCodes: { required: false, type: () => [String] }, message: { required: false, type: () => String } };
    }
}
exports.TwoFactorEnabledResponse = TwoFactorEnabledResponse;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['true'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsEnum)(['true']),
    __metadata("design:type", Boolean)
], TwoFactorEnabledResponse.prototype, "enabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'One-time backup codes for account recovery', type: [String], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], TwoFactorEnabledResponse.prototype, "backupCodes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "Two-factor authentication has been successfully enabled", required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TwoFactorEnabledResponse.prototype, "message", void 0);
//# sourceMappingURL=twoFactorEnabledResponse.dto.js.map