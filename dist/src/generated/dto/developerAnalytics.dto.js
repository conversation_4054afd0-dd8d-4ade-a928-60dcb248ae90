"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeveloperAnalytics = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class DeveloperAnalytics {
    static _OPENAPI_METADATA_FACTORY() {
        return { period: { required: false, type: () => Object }, totalRevenue: { required: false, type: () => Number }, totalDownloads: { required: false, type: () => Number }, totalViews: { required: false, type: () => Number }, activeItems: { required: false, type: () => Number }, topPerformingItems: { required: false, type: () => [Object] }, revenueByPeriod: { required: false, type: () => [Object] }, categoryBreakdown: { required: false, type: () => Object } };
    }
}
exports.DeveloperAnalytics = DeveloperAnalytics;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['7d', '30d', '90d', '1y', 'all'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['7d', '30d', '90d', '1y', 'all']),
    __metadata("design:type", String)
], DeveloperAnalytics.prototype, "period", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DeveloperAnalytics.prototype, "totalRevenue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DeveloperAnalytics.prototype, "totalDownloads", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DeveloperAnalytics.prototype, "totalViews", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of published marketplace items', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DeveloperAnalytics.prototype, "activeItems", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], DeveloperAnalytics.prototype, "topPerformingItems", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [Object], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], DeveloperAnalytics.prototype, "revenueByPeriod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], DeveloperAnalytics.prototype, "categoryBreakdown", void 0);
//# sourceMappingURL=developerAnalytics.dto.js.map