"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PublishRepositoryRequest = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class PublishRepositoryRequest {
    static _OPENAPI_METADATA_FACTORY() {
        return { category: { required: true, type: () => String }, tags: { required: false, type: () => [String] }, visibility: { required: false, type: () => Object }, featured: { required: false, type: () => Boolean }, customDescription: { required: false, type: () => String }, customName: { required: false, type: () => String } };
    }
}
exports.PublishRepositoryRequest = PublishRepositoryRequest;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Marketplace category for the item' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PublishRepositoryRequest.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tags for better discoverability', type: [String], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], PublishRepositoryRequest.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Marketplace visibility', enum: ['public', 'private'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['public', 'private']),
    __metadata("design:type", String)
], PublishRepositoryRequest.prototype, "visibility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Request featured placement (admin approval required)', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PublishRepositoryRequest.prototype, "featured", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Override repository description for marketplace', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PublishRepositoryRequest.prototype, "customDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Override repository name for marketplace', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PublishRepositoryRequest.prototype, "customName", void 0);
//# sourceMappingURL=publishRepositoryRequest.dto.js.map