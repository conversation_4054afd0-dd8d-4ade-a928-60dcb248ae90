"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenResponse = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class TokenResponse {
    static _OPENAPI_METADATA_FACTORY() {
        return { accessToken: { required: false, type: () => String }, refreshToken: { required: false, type: () => String }, expiresIn: { required: false, type: () => Number }, tokenType: { required: false, type: () => String } };
    }
}
exports.TokenResponse = TokenResponse;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'JWT access token (typically expires in 15-30 minutes)', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TokenResponse.prototype, "accessToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Refresh token for obtaining new access tokens (rotated on each use)', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TokenResponse.prototype, "refreshToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Access token expiration time in seconds', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], TokenResponse.prototype, "expiresIn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['Bearer'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['Bearer']),
    __metadata("design:type", String)
], TokenResponse.prototype, "tokenType", void 0);
//# sourceMappingURL=tokenResponse.dto.js.map