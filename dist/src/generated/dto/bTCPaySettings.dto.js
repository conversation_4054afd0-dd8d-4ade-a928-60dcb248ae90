"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BTCPaySettings = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class BTCPaySettings {
    static _OPENAPI_METADATA_FACTORY() {
        return { serverUrl: { required: false, type: () => String }, storeId: { required: false, type: () => String }, webhookSecret: { required: false, type: () => String }, networkFeePolicy: { required: false, type: () => Object } };
    }
}
exports.BTCPaySettings = BTCPaySettings;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'BTCPay Server URL', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BTCPaySettings.prototype, "serverUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'BTCPay Store ID for payouts', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BTCPaySettings.prototype, "storeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Webhook secret for BTCPay notifications', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BTCPaySettings.prototype, "webhookSecret", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Who pays Bitcoin network fees', enum: ['platform_pays', 'developer_pays', 'split'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['platform_pays', 'developer_pays', 'split']),
    __metadata("design:type", String)
], BTCPaySettings.prototype, "networkFeePolicy", void 0);
//# sourceMappingURL=bTCPaySettings.dto.js.map