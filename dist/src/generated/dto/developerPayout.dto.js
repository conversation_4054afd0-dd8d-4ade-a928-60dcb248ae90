"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeveloperPayout = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class DeveloperPayout {
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: false, type: () => String }, amount: { required: false, type: () => Number }, currency: { required: false, type: () => Object }, status: { required: false, type: () => Object }, paymentMethod: { required: false, type: () => String }, transactionId: { required: false, type: () => String }, period: { required: false, type: () => Object }, itemsIncluded: { required: false, type: () => [Object] }, createdAt: { required: false, type: () => String }, processedAt: { required: false, type: () => String }, failureReason: { required: false, type: () => String } };
    }
}
exports.DeveloperPayout = DeveloperPayout;
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperPayout.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DeveloperPayout.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['USD', 'EUR', 'GBP'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['USD', 'EUR', 'GBP']),
    __metadata("design:type", String)
], DeveloperPayout.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['pending', 'processing', 'completed', 'failed'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['pending', 'processing', 'completed', 'failed']),
    __metadata("design:type", String)
], DeveloperPayout.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['btcpay_bitcoin'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['btcpay_bitcoin']),
    __metadata("design:type", String)
], DeveloperPayout.prototype, "paymentMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperPayout.prototype, "transactionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], DeveloperPayout.prototype, "period", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [Object], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], DeveloperPayout.prototype, "itemsIncluded", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperPayout.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperPayout.prototype, "processedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperPayout.prototype, "failureReason", void 0);
//# sourceMappingURL=developerPayout.dto.js.map