"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PayoutRequest = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class PayoutRequest {
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: false, type: () => String }, developerId: { required: false, type: () => String }, amount: { required: false, type: () => Number }, btcAmount: { required: false, type: () => Number }, btcAddress: { required: false, type: () => String }, btcRate: { required: false, type: () => Number }, status: { required: false, type: () => Object }, requestType: { required: false, type: () => Object }, reason: { required: false, type: () => String }, requestedAt: { required: false, type: () => String }, approvedAt: { required: false, type: () => String }, processedAt: { required: false, type: () => String }, completedAt: { required: false, type: () => String }, btcpayInvoiceId: { required: false, type: () => String }, transactionId: { required: false, type: () => String }, networkFee: { required: false, type: () => Number }, failureReason: { required: false, type: () => String } };
    }
}
exports.PayoutRequest = PayoutRequest;
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PayoutRequest.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PayoutRequest.prototype, "developerId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Requested amount in USD', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], PayoutRequest.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Amount in Bitcoin at time of request', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], PayoutRequest.prototype, "btcAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Bitcoin address for payout', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PayoutRequest.prototype, "btcAddress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'USD to Bitcoin rate at time of request', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], PayoutRequest.prototype, "btcRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['pending', 'approved', 'processing', 'completed', 'failed', 'cancelled'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['pending', 'approved', 'processing', 'completed', 'failed', 'cancelled']),
    __metadata("design:type", String)
], PayoutRequest.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['manual', 'automatic'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['manual', 'automatic']),
    __metadata("design:type", String)
], PayoutRequest.prototype, "requestType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reason for manual request', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PayoutRequest.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PayoutRequest.prototype, "requestedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PayoutRequest.prototype, "approvedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PayoutRequest.prototype, "processedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PayoutRequest.prototype, "completedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'BTCPay Server invoice ID for the payout', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PayoutRequest.prototype, "btcpayInvoiceId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Bitcoin transaction ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PayoutRequest.prototype, "transactionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Bitcoin network fee paid', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], PayoutRequest.prototype, "networkFee", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reason for failure if status is failed', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PayoutRequest.prototype, "failureReason", void 0);
//# sourceMappingURL=payoutRequest.dto.js.map