"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DesktopRegistrationResponse = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class DesktopRegistrationResponse {
    static _OPENAPI_METADATA_FACTORY() {
        return { registered: { required: false, type: () => Boolean }, sessionId: { required: false, type: () => String }, deviceId: { required: false, type: () => String }, maxBotSessions: { required: false, type: () => Number }, transfersRemaining: { required: false, type: () => Number } };
    }
}
exports.DesktopRegistrationResponse = DesktopRegistrationResponse;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['true'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsEnum)(['true']),
    __metadata("design:type", Boolean)
], DesktopRegistrationResponse.prototype, "registered", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DesktopRegistrationResponse.prototype, "sessionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DesktopRegistrationResponse.prototype, "deviceId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Bot sessions allowed on this device', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DesktopRegistrationResponse.prototype, "maxBotSessions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Device transfers remaining this period', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DesktopRegistrationResponse.prototype, "transfersRemaining", void 0);
//# sourceMappingURL=desktopRegistrationResponse.dto.js.map