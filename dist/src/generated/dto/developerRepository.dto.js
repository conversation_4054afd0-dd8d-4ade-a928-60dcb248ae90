"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeveloperRepository = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class DeveloperRepository {
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: false, type: () => Number }, name: { required: false, type: () => String }, fullName: { required: false, type: () => String }, description: { required: false, type: () => String }, private: { required: false, type: () => Boolean }, fork: { required: false, type: () => Boolean }, size: { required: false, type: () => Number }, language: { required: false, type: () => String }, stars: { required: false, type: () => Number }, forks: { required: false, type: () => Number }, openIssues: { required: false, type: () => Number }, defaultBranch: { required: false, type: () => String }, createdAt: { required: false, type: () => String }, updatedAt: { required: false, type: () => String }, pushedAt: { required: false, type: () => String }, cloneUrl: { required: false, type: () => String }, sshUrl: { required: false, type: () => String }, htmlUrl: { required: false, type: () => String }, isPublished: { required: false, type: () => Boolean }, marketplaceItemId: { required: false, type: () => String }, hasMarketplaceMetadata: { required: false, type: () => Boolean } };
    }
}
exports.DeveloperRepository = DeveloperRepository;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Gitea repository ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DeveloperRepository.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Repository name', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperRepository.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Full repository name (owner/repo)', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperRepository.prototype, "fullName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperRepository.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], DeveloperRepository.prototype, "private", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], DeveloperRepository.prototype, "fork", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Repository size in KB', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DeveloperRepository.prototype, "size", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Primary programming language', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperRepository.prototype, "language", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DeveloperRepository.prototype, "stars", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DeveloperRepository.prototype, "forks", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DeveloperRepository.prototype, "openIssues", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperRepository.prototype, "defaultBranch", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperRepository.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperRepository.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperRepository.prototype, "pushedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperRepository.prototype, "cloneUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperRepository.prototype, "sshUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperRepository.prototype, "htmlUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether this repo is published to marketplace', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], DeveloperRepository.prototype, "isPublished", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Associated marketplace item ID if published', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeveloperRepository.prototype, "marketplaceItemId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether repo contains required marketplace metadata', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], DeveloperRepository.prototype, "hasMarketplaceMetadata", void 0);
//# sourceMappingURL=developerRepository.dto.js.map