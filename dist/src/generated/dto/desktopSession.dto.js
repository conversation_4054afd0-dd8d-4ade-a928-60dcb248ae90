"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DesktopSession = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class DesktopSession {
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: false, type: () => String }, platform: { required: false, type: () => String }, deviceInfo: { required: false, type: () => Object }, registeredAt: { required: false, type: () => String }, lastActivityAt: { required: false, type: () => String }, isActive: { required: false, type: () => Boolean }, isCurrent: { required: false, type: () => Boolean }, activeBotSessions: { required: false, type: () => Number }, maxBotSessions: { required: false, type: () => Number }, transferable: { required: false, type: () => Boolean } };
    }
}
exports.DesktopSession = DesktopSession;
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DesktopSession.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['desktop'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['desktop']),
    __metadata("design:type", String)
], DesktopSession.prototype, "platform", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], DesktopSession.prototype, "deviceInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DesktopSession.prototype, "registeredAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DesktopSession.prototype, "lastActivityAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], DesktopSession.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], DesktopSession.prototype, "isCurrent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of active bot sessions on this device', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DesktopSession.prototype, "activeBotSessions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Maximum bot sessions allowed on this device', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DesktopSession.prototype, "maxBotSessions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether this device can be transferred to another', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], DesktopSession.prototype, "transferable", void 0);
//# sourceMappingURL=desktopSession.dto.js.map