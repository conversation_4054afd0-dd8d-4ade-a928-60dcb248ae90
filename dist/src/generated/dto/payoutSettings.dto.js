"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PayoutSettings = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class PayoutSettings {
    static _OPENAPI_METADATA_FACTORY() {
        return { minimumAmount: { required: false, type: () => Number }, frequency: { required: false, type: () => Object }, paymentMethod: { required: false, type: () => String }, revenueShare: { required: false, type: () => Object }, autoPayoutEnabled: { required: false, type: () => Boolean }, holdingPeriod: { required: false, type: () => Number }, currency: { required: false, type: () => Object } };
    }
}
exports.PayoutSettings = PayoutSettings;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Minimum payout amount in USD', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], PayoutSettings.prototype, "minimumAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'How often payouts are processed', enum: ['daily', 'weekly', 'biweekly', 'monthly', 'quarterly'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['daily', 'weekly', 'biweekly', 'monthly', 'quarterly']),
    __metadata("design:type", String)
], PayoutSettings.prototype, "frequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['btcpay_bitcoin'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['btcpay_bitcoin']),
    __metadata("design:type", String)
], PayoutSettings.prototype, "paymentMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], PayoutSettings.prototype, "revenueShare", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether to automatically process payouts', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PayoutSettings.prototype, "autoPayoutEnabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Days to hold revenue before payout eligibility', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], PayoutSettings.prototype, "holdingPeriod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['USD', 'EUR', 'GBP'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['USD', 'EUR', 'GBP']),
    __metadata("design:type", String)
], PayoutSettings.prototype, "currency", void 0);
//# sourceMappingURL=payoutSettings.dto.js.map