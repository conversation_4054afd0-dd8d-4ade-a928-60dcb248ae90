"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BackupCodesResponse = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class BackupCodesResponse {
    static _OPENAPI_METADATA_FACTORY() {
        return { codes: { required: false, type: () => [String] }, generatedAt: { required: false, type: () => String }, usedCodes: { required: false, type: () => [String] }, remainingCodes: { required: false, type: () => Number } };
    }
}
exports.BackupCodesResponse = BackupCodesResponse;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'List of 8-character backup codes', type: [String], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], BackupCodesResponse.prototype, "codes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'When these codes were generated', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BackupCodesResponse.prototype, "generatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'List of already used backup codes (masked)', type: [String], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], BackupCodesResponse.prototype, "usedCodes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of unused backup codes', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], BackupCodesResponse.prototype, "remainingCodes", void 0);
//# sourceMappingURL=backupCodesResponse.dto.js.map