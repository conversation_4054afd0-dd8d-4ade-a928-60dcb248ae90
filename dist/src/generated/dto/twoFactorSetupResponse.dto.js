"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwoFactorSetupResponse = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class TwoFactorSetupResponse {
    static _OPENAPI_METADATA_FACTORY() {
        return { secret: { required: false, type: () => String }, qrCodeUrl: { required: false, type: () => String }, backupCodes: { required: false, type: () => [String] }, manualEntryKey: { required: false, type: () => String }, issuer: { required: false, type: () => String } };
    }
}
exports.TwoFactorSetupResponse = TwoFactorSetupResponse;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Base32-encoded TOTP secret for manual entry', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TwoFactorSetupResponse.prototype, "secret", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Data URL for QR code image', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TwoFactorSetupResponse.prototype, "qrCodeUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'One-time backup codes (only shown during setup)', type: [String], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], TwoFactorSetupResponse.prototype, "backupCodes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Human-readable secret key for manual entry', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TwoFactorSetupResponse.prototype, "manualEntryKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Service name for the authenticator app', example: "RSGlider", required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TwoFactorSetupResponse.prototype, "issuer", void 0);
//# sourceMappingURL=twoFactorSetupResponse.dto.js.map