"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GiteaProfile = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class GiteaProfile {
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: false, type: () => Number }, username: { required: false, type: () => String }, fullName: { required: false, type: () => String }, email: { required: false, type: () => String }, avatarUrl: { required: false, type: () => String }, profileUrl: { required: false, type: () => String }, publicRepos: { required: false, type: () => Number }, privateRepos: { required: false, type: () => Number }, totalRepos: { required: false, type: () => Number }, followers: { required: false, type: () => Number }, following: { required: false, type: () => Number }, provisionedAt: { required: false, type: () => String }, lastSyncAt: { required: false, type: () => String }, syncStatus: { required: false, type: () => Object }, accountStatus: { required: false, type: () => Object }, ssoEnabled: { required: false, type: () => Boolean }, adminProvisioned: { required: false, type: () => Boolean }, provisionedBy: { required: false, type: () => String } };
    }
}
exports.GiteaProfile = GiteaProfile;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Gitea user ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GiteaProfile.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Gitea username', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GiteaProfile.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User\'s full name in Gitea', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GiteaProfile.prototype, "fullName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], GiteaProfile.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GiteaProfile.prototype, "avatarUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Direct link to git.rsglider.com profile', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GiteaProfile.prototype, "profileUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GiteaProfile.prototype, "publicRepos", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GiteaProfile.prototype, "privateRepos", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GiteaProfile.prototype, "totalRepos", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GiteaProfile.prototype, "followers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GiteaProfile.prototype, "following", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'When the Gitea account was provisioned', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GiteaProfile.prototype, "provisionedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GiteaProfile.prototype, "lastSyncAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['active', 'syncing', 'error', 'disabled'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['active', 'syncing', 'error', 'disabled']),
    __metadata("design:type", String)
], GiteaProfile.prototype, "syncStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['active', 'disabled', 'archived'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['active', 'disabled', 'archived']),
    __metadata("design:type", String)
], GiteaProfile.prototype, "accountStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether SSO login is enabled', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], GiteaProfile.prototype, "ssoEnabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether account was provisioned by admin', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], GiteaProfile.prototype, "adminProvisioned", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Admin username who provisioned the account', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GiteaProfile.prototype, "provisionedBy", void 0);
//# sourceMappingURL=giteaProfile.dto.js.map