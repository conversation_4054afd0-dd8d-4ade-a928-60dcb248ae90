"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePayoutSettingsRequest = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class UpdatePayoutSettingsRequest {
    static _OPENAPI_METADATA_FACTORY() {
        return { minimumAmount: { required: false, type: () => Number, minimum: 1 }, frequency: { required: false, type: () => Object }, revenueShare: { required: false, type: () => Object }, autoPayoutEnabled: { required: false, type: () => Boolean }, holdingPeriod: { required: false, type: () => Number, maximum: 365 } };
    }
}
exports.UpdatePayoutSettingsRequest = UpdatePayoutSettingsRequest;
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], UpdatePayoutSettingsRequest.prototype, "minimumAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['daily', 'weekly', 'biweekly', 'monthly', 'quarterly'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['daily', 'weekly', 'biweekly', 'monthly', 'quarterly']),
    __metadata("design:type", String)
], UpdatePayoutSettingsRequest.prototype, "frequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], UpdatePayoutSettingsRequest.prototype, "revenueShare", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdatePayoutSettingsRequest.prototype, "autoPayoutEnabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Max)(365),
    __metadata("design:type", Number)
], UpdatePayoutSettingsRequest.prototype, "holdingPeriod", void 0);
//# sourceMappingURL=updatePayoutSettingsRequest.dto.js.map