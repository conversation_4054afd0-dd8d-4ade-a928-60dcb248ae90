export declare class GiteaProfile {
    id?: number;
    username?: string;
    fullName?: string;
    email?: string;
    avatarUrl?: string;
    profileUrl?: string;
    publicRepos?: number;
    privateRepos?: number;
    totalRepos?: number;
    followers?: number;
    following?: number;
    provisionedAt?: string;
    lastSyncAt?: string;
    syncStatus?: 'active' | 'syncing' | 'error' | 'disabled';
    accountStatus?: 'active' | 'disabled' | 'archived';
    ssoEnabled?: boolean;
    adminProvisioned?: boolean;
    provisionedBy?: string;
}
