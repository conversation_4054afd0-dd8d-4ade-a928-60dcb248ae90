"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GiteaWebhookPayload = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class GiteaWebhookPayload {
    static _OPENAPI_METADATA_FACTORY() {
        return { action: { required: false, type: () => Object }, repository: { required: false, type: () => Object }, pusher: { required: false, type: () => Object }, commits: { required: false, type: () => [Object] }, release: { required: false, type: () => Object } };
    }
}
exports.GiteaWebhookPayload = GiteaWebhookPayload;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['created', 'deleted', 'pushed', 'released'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['created', 'deleted', 'pushed', 'released']),
    __metadata("design:type", String)
], GiteaWebhookPayload.prototype, "action", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], GiteaWebhookPayload.prototype, "repository", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], GiteaWebhookPayload.prototype, "pusher", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [Object], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], GiteaWebhookPayload.prototype, "commits", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], GiteaWebhookPayload.prototype, "release", void 0);
//# sourceMappingURL=giteaWebhookPayload.dto.js.map