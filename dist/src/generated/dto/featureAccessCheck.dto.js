"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FeatureAccessCheck = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class FeatureAccessCheck {
    static _OPENAPI_METADATA_FACTORY() {
        return { featureKey: { required: false, type: () => String }, hasAccess: { required: false, type: () => Boolean }, reason: { required: false, type: () => Object }, maxInstances: { required: false, type: () => Number }, availableInstances: { required: false, type: () => Number }, nextAvailableAt: { required: false, type: () => String }, upgradeOptions: { required: false, type: () => [Object] } };
    }
}
exports.FeatureAccessCheck = FeatureAccessCheck;
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FeatureAccessCheck.prototype, "featureKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], FeatureAccessCheck.prototype, "hasAccess", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['granted', 'no_subscription', 'expired', 'instance_limit_exceeded', 'feature_disabled'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['granted', 'no_subscription', 'expired', 'instance_limit_exceeded', 'feature_disabled']),
    __metadata("design:type", String)
], FeatureAccessCheck.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FeatureAccessCheck.prototype, "maxInstances", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FeatureAccessCheck.prototype, "availableInstances", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FeatureAccessCheck.prototype, "nextAvailableAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], FeatureAccessCheck.prototype, "upgradeOptions", void 0);
//# sourceMappingURL=featureAccessCheck.dto.js.map