"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BotSession = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class BotSession {
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: false, type: () => String }, sessionName: { required: false, type: () => String }, botType: { required: false, type: () => String }, desktopSessionId: { required: false, type: () => String }, deviceName: { required: false, type: () => String }, status: { required: false, type: () => Object }, configuration: { required: false, type: () => Object }, statistics: { required: false, type: () => Object }, lastActivityAt: { required: false, type: () => String }, canTransfer: { required: false, type: () => Boolean }, subscriptionTier: { required: false, type: () => String } };
    }
}
exports.BotSession = BotSession;
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BotSession.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User-friendly name for this bot session', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BotSession.prototype, "sessionName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Type of bot running', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BotSession.prototype, "botType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Associated desktop session', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BotSession.prototype, "desktopSessionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Device where bot is running', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BotSession.prototype, "deviceName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['starting', 'running', 'paused', 'stopping', 'stopped', 'error'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['starting', 'running', 'paused', 'stopping', 'stopped', 'error']),
    __metadata("design:type", String)
], BotSession.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Bot-specific configuration', required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], BotSession.prototype, "configuration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], BotSession.prototype, "statistics", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BotSession.prototype, "lastActivityAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether this bot session can be transferred', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], BotSession.prototype, "canTransfer", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Subscription tier that allows this bot', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BotSession.prototype, "subscriptionTier", void 0);
//# sourceMappingURL=botSession.dto.js.map