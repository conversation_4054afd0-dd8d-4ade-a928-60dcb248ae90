"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionLimits = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class SessionLimits {
    static _OPENAPI_METADATA_FACTORY() {
        return { webSessions: { required: false, type: () => Object }, desktopSessions: { required: false, type: () => Object }, botSessions: { required: false, type: () => Object }, subscriptionTier: { required: false, type: () => Object }, addons: { required: false, type: () => Object }, upgradeRequired: { required: false, type: () => Boolean } };
    }
}
exports.SessionLimits = SessionLimits;
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], SessionLimits.prototype, "webSessions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], SessionLimits.prototype, "desktopSessions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], SessionLimits.prototype, "botSessions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Current subscription tier', enum: ['free', 'pro'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['free', 'pro']),
    __metadata("design:type", String)
], SessionLimits.prototype, "subscriptionTier", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Active addon purchases', example: { "desktopAddons": 2, "botAddons": 5 }, required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], SessionLimits.prototype, "addons", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether upgrade is needed for more sessions', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SessionLimits.prototype, "upgradeRequired", void 0);
//# sourceMappingURL=sessionLimits.dto.js.map