{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../../../src/modules/auth/strategies/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAmE;AACnE,+CAAoD;AACpD,+CAAoD;AACpD,2CAA+C;AAE/C,6DAAyD;AAGlD,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IACzD,YACmB,aAA4B,EAC5B,YAA0B;QAE3C,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC;SACrD,CAAC,CAAC;QAPc,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAc;IAO7C,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAY;QACzB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAE3D,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,8BAAqB,CAAC,gBAAgB,CAAC,CAAC;QACpD,CAAC;QAED,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC;SAC9B,CAAC;IACJ,CAAC;CACF,CAAA;AAzBY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAGuB,sBAAa;QACd,4BAAY;GAHlC,WAAW,CAyBvB"}