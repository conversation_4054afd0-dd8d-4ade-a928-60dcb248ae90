import { User } from '@/users/entities/user.entity';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Repository } from 'typeorm';
import { AuthResponse } from './dto/auth-response.dto';
import { LoginRequest } from './dto/login-request.dto';
import { RegisterRequest } from './dto/register-request.dto';
import { TokenResponse } from './dto/token-response.dto';
export declare class AuthService {
    private readonly userRepository;
    private readonly jwtService;
    private readonly configService;
    constructor(userRepository: Repository<User>, jwtService: JwtService, configService: ConfigService);
    registerUser(registerData: RegisterRequest): Promise<AuthResponse>;
    loginUser(loginData: LoginRequest): Promise<AuthResponse>;
    logoutUser(): Promise<{
        message: string;
    }>;
    refreshToken(refreshTokenData: {
        refreshToken: string;
    }): Promise<TokenResponse>;
    forgotPassword(forgotPasswordData: {
        email: string;
    }): Promise<{
        message: string;
    }>;
    resetPassword(resetData: {
        token: string;
        password: string;
    }): Promise<{
        message: string;
    }>;
    private generateTokens;
}
