import { AuthService } from './auth.service';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    registerUser(body: any): Promise<any>;
    loginUser(body: any): Promise<any>;
    logoutUser(): Promise<any>;
    refreshToken(body: any): Promise<any>;
    forgotPassword(body: any): Promise<any>;
    resetPassword(body: any): Promise<any>;
}
