import { AuthService } from './auth.service';
import { AuthResponse } from './dto/auth-response.dto';
import { TokenResponse } from './dto/token-response.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    registerUser(body: any): Promise<AuthResponse>;
    loginUser(body: any): Promise<AuthResponse>;
    logoutUser(): Promise<{
        message: string;
    }>;
    refreshToken(body: any): Promise<TokenResponse>;
    forgotPassword(body: any): Promise<{
        message: string;
    }>;
    resetPassword(body: any): Promise<{
        message: string;
    }>;
}
