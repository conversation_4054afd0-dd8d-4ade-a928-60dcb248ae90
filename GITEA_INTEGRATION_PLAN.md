# RSGlider ↔ Gitea Integration Implementation Plan

## 🎯 Overview
This document outlines the comprehensive implementation plan for integrating RSGlider with Gitea to create a developer marketplace where users can publish and sell their scripts/tools directly from their repositories.

**Key Architecture Change**: Admin-controlled provisioning with seamless SSO access instead of user-initiated OAuth.

## 🏗️ Architecture Components

### 1. Admin-Controlled Provisioning Flow
```
Admin Promotes User → Auto Gitea Account Creation → SSO Setup → Full Sync
```

**Implementation Steps:**
1. **Gitea Admin API Integration**
   - Use Gitea Admin API for account creation/management
   - Configure admin service account with full privileges
   - Set up automated account provisioning

2. **SSO Integration**
   - Configure Gitea to use RSGlider as SSO provider
   - Set up OIDC/SAML integration for seamless login
   - Map RSGlider roles to Gitea permissions

3. **Automatic Sync System**
   - Real-time bidirectional sync between platforms
   - Webhook-based repository change detection
   - Scheduled sync jobs for data consistency

### 2. SSO Configuration

**Gitea SSO Setup:**
```yaml
# Gitea app.ini configuration
[oauth2_client]
ENABLE_AUTO_REGISTRATION = true
USERNAME = email
UPDATE_AVATAR = true
ACCOUNT_LINKING = auto

[openid]
ENABLE_OPENID_SIGNIN = true
ENABLE_OPENID_SIGNUP = true

# RSGlider OIDC Provider Configuration
[auth.rsglider]
PROVIDER = openidConnect
CLIENT_ID = gitea-integration
CLIENT_SECRET = ${OIDC_CLIENT_SECRET}
OPENID_CONNECT_AUTO_DISCOVERY_URL = https://api.rsglider.com/.well-known/openid_configuration
ICON_URL = https://rsglider.com/logo.png
```

**RSGlider OIDC Provider:**
- Implement OIDC provider endpoints
- Map RSGlider user data to OIDC claims
- Handle Gitea-specific claim mappings
- Automatic role synchronization

### 3. Database Schema Extensions

**New Tables:**
```sql
-- Gitea integrations (admin-provisioned)
gitea_integrations (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  gitea_user_id INTEGER,
  gitea_username VARCHAR(255),
  gitea_email VARCHAR(255),
  account_status VARCHAR(20) DEFAULT 'active', -- active, disabled, archived
  sync_status VARCHAR(20) DEFAULT 'active', -- active, syncing, error, disabled
  sso_enabled BOOLEAN DEFAULT true,
  admin_provisioned BOOLEAN DEFAULT true,
  provisioned_by UUID REFERENCES users(id),
  provisioned_at TIMESTAMP DEFAULT NOW(),
  last_sync_at TIMESTAMP,
  sync_errors TEXT[]
);

-- Repository marketplace items
marketplace_repositories (
  id UUID PRIMARY KEY,
  store_item_id UUID REFERENCES store_items(id),
  gitea_integration_id UUID REFERENCES gitea_integrations(id),
  repository_id INTEGER,
  repository_name VARCHAR(255),
  repository_full_name VARCHAR(255),
  last_sync_at TIMESTAMP,
  version VARCHAR(100),
  auto_sync_enabled BOOLEAN DEFAULT true
);

-- Developer analytics
developer_analytics (
  id UUID PRIMARY KEY,
  developer_id UUID REFERENCES users(id),
  item_id UUID REFERENCES store_items(id),
  date DATE,
  views INTEGER DEFAULT 0,
  downloads INTEGER DEFAULT 0,
  revenue DECIMAL(10,2) DEFAULT 0,
  unique_users INTEGER DEFAULT 0
);

-- Developer payouts
developer_payouts (
  id UUID PRIMARY KEY,
  developer_id UUID REFERENCES users(id),
  amount DECIMAL(10,2),
  currency VARCHAR(3),
  status VARCHAR(20),
  payment_method VARCHAR(50),
  transaction_id VARCHAR(255),
  period_start DATE,
  period_end DATE,
  created_at TIMESTAMP,
  processed_at TIMESTAMP,
  failure_reason TEXT
);
```

### 3. Repository Metadata Standard

**Required files in repository:**
- `marketplace.yml` - Marketplace configuration
- `README.md` - Description and documentation
- `LICENSE` - License information

**marketplace.yml format:**
```yaml
marketplace:
  name: "Custom Script Name"
  description: "Brief description"
  category: "automation" # scripts, tools, plugins, themes
  tags: ["automation", "productivity"]
  pricing:
    type: "one_time" # free, one_time, subscription, pay_what_you_want
    price: 9.99
    currency: "USD"
  requirements:
    - "Python 3.8+"
    - "requests library"
  features:
    - "Feature 1"
    - "Feature 2"
  screenshots:
    - "screenshots/demo1.png"
    - "screenshots/demo2.png"
  version: "1.0.0"
  compatibility: ["windows", "linux", "macos"]
```

## 🔄 Implementation Phases

### Phase 1: Admin Provisioning & SSO Setup (Week 1-2)
- [ ] Gitea Admin API integration setup
- [ ] Database schema creation for admin-controlled accounts
- [ ] SSO/OIDC configuration between RSGlider and Gitea
- [ ] Admin promotion endpoint with auto-provisioning
- [ ] Basic Gitea account creation and management

### Phase 2: Repository Management (Week 3-4)
- [ ] Repository listing and filtering
- [ ] Metadata parsing from repositories
- [ ] Repository-to-marketplace item conversion
- [ ] Publish/unpublish functionality
- [ ] Manual sync capabilities

### Phase 3: Webhook Integration (Week 5)
- [ ] Gitea webhook endpoint setup
- [ ] Webhook signature verification
- [ ] Automatic sync on repository changes
- [ ] Version management and releases
- [ ] Error handling and retry logic

### Phase 4: Analytics & Revenue (Week 6-7)
- [ ] Analytics data collection
- [ ] Developer dashboard implementation
- [ ] Revenue tracking and calculations
- [ ] Payout system integration
- [ ] Reporting and insights

### Phase 5: Admin Tools (Week 8)
- [ ] Developer promotion/revocation system
- [ ] Marketplace moderation tools
- [ ] Revenue sharing configuration
- [ ] Analytics and reporting for admins
- [ ] Audit trails and logging

## 🔐 Security Considerations

### OAuth Security
- **State parameter validation** - Prevent CSRF attacks
- **Token encryption** - Store tokens encrypted at rest
- **Scope limitation** - Request minimal required permissions
- **Token rotation** - Implement refresh token rotation

### Webhook Security
- **Signature verification** - Validate webhook signatures
- **IP allowlisting** - Restrict webhook sources
- **Rate limiting** - Prevent webhook spam
- **Payload validation** - Validate webhook payloads

### Marketplace Security
- **Content scanning** - Scan repositories for malicious content
- **License validation** - Ensure proper licensing
- **Metadata validation** - Validate marketplace.yml files
- **Revenue protection** - Prevent revenue manipulation

## 💰 Revenue Sharing Model

### Default Revenue Split
- **Developer**: 70%
- **Platform**: 30%

### Flexible Payout Configuration
- **Default minimum payout**: $50 USD equivalent in Bitcoin
- **Default frequency**: Weekly (configurable per user/role)
- **Payment method**: Bitcoin via BTCPay Server
- **Automatic conversion**: USD revenue → Bitcoin at current market rate
- **Admin controls**: Set different schedules per user or role
- **Available frequencies**: Daily, Weekly, Bi-weekly, Monthly, Quarterly

### Payout Hierarchy (Priority Order)
1. **User-specific settings** (highest priority)
2. **Role-based settings** (by role priority)
3. **Global default settings** (fallback)

### BTCPay Server Integration
- **Network fee policy**: Platform pays (configurable)
- **Automatic payouts**: Enabled by default
- **Holding period**: 7 days (configurable)
- **Address verification**: Required before first payout

### Fee Structure
- **Transaction fee**: 2.9% + $0.30 (passed to developer)
- **Platform fee**: 30% of net revenue (configurable per role/user)
- **Bitcoin network fees**: Platform pays by default

## 🚀 Deployment Strategy

### Environment Setup
1. **Development**: Local Gitea instance for testing
2. **Staging**: Staging Gitea with test repositories
3. **Production**: Production Gitea integration

### Configuration Management
```yaml
gitea:
  base_url: "https://git.rsglider.com"
  admin_api:
    token: "${GITEA_ADMIN_TOKEN}"
    base_url: "https://git.rsglider.com/api/v1"
  sso:
    oidc_provider: "https://api.rsglider.com"
    client_id: "${GITEA_OIDC_CLIENT_ID}"
    client_secret: "${GITEA_OIDC_CLIENT_SECRET}"
  webhook:
    secret: "${GITEA_WEBHOOK_SECRET}"

btcpay:
  server_url: "https://btcpay.rsglider.com"
  store_id: "${BTCPAY_STORE_ID}"
  api_key: "${BTCPAY_API_KEY}"
  webhook_secret: "${BTCPAY_WEBHOOK_SECRET}"
  network_fee_policy: "platform_pays"

marketplace:
  revenue_share:
    developer_percentage: 70
    platform_percentage: 30
  payout:
    default_minimum_amount: 50
    default_frequency: "weekly"
    default_holding_period: 7
    payment_method: "btcpay_bitcoin"
    auto_payout_enabled: true
```

## 📊 Success Metrics

### Developer Adoption
- Number of developers with Gitea integration
- Number of published repositories
- Developer retention rate
- Average revenue per developer

### Marketplace Performance
- Total marketplace revenue
- Download/purchase conversion rates
- User engagement metrics
- Repository quality scores

### Technical Metrics
- OAuth success rate
- Webhook processing reliability
- Sync accuracy and speed
- API response times

## 🔧 Technical Implementation Notes

### API Rate Limiting
- Gitea API calls: 5000/hour per user
- Webhook processing: 100/minute
- Analytics queries: 1000/hour per developer

### Caching Strategy
- Repository metadata: 1 hour TTL
- Analytics data: 15 minutes TTL
- User profiles: 30 minutes TTL

### Error Handling
- OAuth failures: Retry with exponential backoff
- Webhook failures: Dead letter queue
- Sync failures: Manual retry option
- Payment failures: Notification system

This implementation plan provides a comprehensive roadmap for building a robust developer marketplace integrated with Gitea, enabling users to monetize their repositories while providing a seamless experience for both developers and customers.
