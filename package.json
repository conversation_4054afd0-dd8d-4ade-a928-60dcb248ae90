{"name": "rsglider-api", "version": "1.0.0", "description": "RSGlider API - Comprehensive platform with developer marketplace, BTCPay integration, and session management", "author": "RSGlider Team", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "validate:api": "spectral lint api-docs/openapi.yaml", "lint:api": "spectral lint api-docs/openapi.yaml --format=stylish", "docker:setup": "./scripts/dev-setup.sh", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:logs:api": "docker-compose logs -f api", "docker:restart:api": "docker-compose restart api", "db:shell": "docker-compose exec postgres psql -U rsglider -d rsglider", "redis:shell": "docker-compose exec redis redis-cli -a rsglider_redis_password", "generate": "ts-node scripts/complete-generator.ts", "generate:watch": "nodemon --exec ts-node scripts/generate-nestjs-code.ts --watch api-docs/openapi.yaml", "prebuild": "npm run generate", "prestart:dev": "npm run generate"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/jwt": "^10.1.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.1.0", "@nestjs/typeorm": "^10.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.0", "typeorm": "^0.3.17", "bcrypt": "^5.1.0", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "pg": "^8.11.3", "redis": "^4.6.10", "connect-redis": "^7.1.0", "express-session": "^1.17.3", "multer": "^1.4.5-lts.1", "@nestjs/throttler": "^5.0.1", "openapi-types": "^12.1.3"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/bcrypt": "^5.0.0", "@types/speakeasy": "^2.0.7", "@types/qrcode": "^1.5.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3", "@stoplight/spectral-cli": "^6.10.0", "js-yaml": "^4.1.0", "@types/js-yaml": "^4.0.8", "@types/multer": "^1.4.11", "@types/express-session": "^1.17.10", "@types/connect-redis": "^0.0.20", "nodemon": "^3.0.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": ">=22.0.0", "npm": ">=10.0.0"}, "keywords": ["<PERSON><PERSON><PERSON>", "api", "openapi", "swagger", "btcpay", "g<PERSON>a", "marketplace", "bitcoin", "developer-platform"], "repository": {"type": "git", "url": "git+https://github.com/rsglider/rsglider-api.git"}, "bugs": {"url": "https://github.com/rsglider/rsglider-api/issues"}, "homepage": "https://docs.rsglider.com", "main": "index.js", "directories": {"doc": "docs"}, "type": "commonjs"}