openapi: 3.0.3
info:
  title: RSGlider API
  description: |
    A comprehensive user management API with role-based access control, 2FA support,
    session management, e-commerce store, and administrative capabilities.

    Note: IDE validation errors about path parameters "required: true" are false positives
    from faulty YAML schema validators. According to OpenAPI 3.0 specification, path
    parameters must have "required: true".
  version: 1.0.0
  contact:
    name: RSGlider Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.rsglider.com/v1
    description: Production server
  - url: https://staging-api.rsglider.com/v1
    description: Staging server
  - url: http://localhost:3000/v1
    description: Development server

security:
  - BearerAuth: []
  - ApiKeyAuth: []

paths:
  # Authentication Endpoints
  /auth/register:
    post:
      tags:
        - Authentication
      summary: Register a new user
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          description: User already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/login:
    post:
      tags:
        - Authentication
      summary: Login user
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '423':
          description: Account locked due to failed attempts
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/logout:
    post:
      tags:
        - Authentication
      summary: Logout user (invalidate current session)
      responses:
        '200':
          description: Logout successful
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/refresh:
    post:
      tags:
        - Authentication
      summary: Refresh access token using refresh token
      description: |
        Exchange a valid refresh token for a new access token. Implements refresh token rotation
        for enhanced security - the old refresh token is invalidated and a new one is provided.
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                refreshToken:
                  type: string
                  description: Valid refresh token from login or previous refresh
              required:
                - refreshToken
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/TokenResponse'
                  - type: object
                    properties:
                      sessionId:
                        type: string
                        format: uuid
                        description: Session ID for tracking purposes
        '401':
          description: Invalid or expired refresh token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '429':
          description: Too many refresh attempts
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/forgot-password:
    post:
      tags:
        - Authentication
      summary: Request password reset
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
              required:
                - email
      responses:
        '200':
          description: Password reset email sent
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/reset-password:
    post:
      tags:
        - Authentication
      summary: Reset password with token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                newPassword:
                  type: string
                  minLength: 8
              required:
                - token
                - newPassword
      responses:
        '200':
          description: Password reset successful
        '400':
          $ref: '#/components/responses/BadRequest'
        '410':
          description: Reset token expired
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'



  # User Profile Endpoints
  /users/me:
    get:
      tags:
        - User Profile
      summary: Get current user profile
      responses:
        '200':
          description: User profile retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          $ref: '#/components/responses/Unauthorized'

    put:
      tags:
        - User Profile
      summary: Update current user profile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /users/me/change-password:
    post:
      tags:
        - User Profile
      summary: Change user password
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                currentPassword:
                  type: string
                newPassword:
                  type: string
                  minLength: 8
              required:
                - currentPassword
                - newPassword
      responses:
        '200':
          description: Password changed successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # Two-Factor Authentication Management
  /users/me/2fa/setup:
    post:
      tags:
        - User Profile
      summary: Setup two-factor authentication
      description: |
        Generate a new TOTP secret and QR code for setting up 2FA.
        User must verify the setup with a valid TOTP code to enable 2FA.
      responses:
        '200':
          description: 2FA setup initiated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TwoFactorSetupResponse'
        '400':
          description: 2FA already enabled
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /users/me/2fa/verify-setup:
    post:
      tags:
        - User Profile
      summary: Verify and enable two-factor authentication
      description: |
        Verify the TOTP code from the authenticator app and enable 2FA for the user.
        Returns backup codes that should be stored securely.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TwoFactorVerifyRequest'
      responses:
        '200':
          description: 2FA enabled successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TwoFactorEnabledResponse'
        '400':
          description: Invalid TOTP code or setup not initiated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /users/me/2fa/disable:
    post:
      tags:
        - User Profile
      summary: Disable two-factor authentication
      description: |
        Disable 2FA for the user account. Requires current password and either
        a valid TOTP code or backup code for security verification.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TwoFactorDisableRequest'
      responses:
        '200':
          description: 2FA disabled successfully
        '400':
          description: Invalid credentials or verification code
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /users/me/2fa/backup-codes:
    get:
      tags:
        - User Profile
      summary: Get backup codes
      description: |
        Retrieve current backup codes. Requires password verification for security.
      parameters:
        - name: password
          in: query
          required: true
          schema:
            type: string
          description: Current user password for verification
      responses:
        '200':
          description: Backup codes retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BackupCodesResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          description: Invalid password or 2FA not enabled
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      tags:
        - User Profile
      summary: Regenerate backup codes
      description: |
        Generate new backup codes and invalidate old ones. Requires password verification.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                password:
                  type: string
                  description: Current user password for verification
              required:
                - password
      responses:
        '200':
          description: New backup codes generated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BackupCodesResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          description: Invalid password or 2FA not enabled
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Session Management
  /users/me/sessions:
    get:
      tags:
        - User Profile
      summary: Get user active sessions across all devices
      description: |
        Retrieve all active sessions for the current user. Sessions are tracked server-side
        regardless of token type (JWT or traditional sessions) for security monitoring.
        Shows device information, location, and last activity.
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
            maximum: 100
      responses:
        '200':
          description: Sessions retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  sessions:
                    type: array
                    items:
                      $ref: '#/components/schemas/Session'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
                  currentSessionId:
                    type: string
                    format: uuid
                    description: ID of the current session

  /users/me/sessions/{sessionId}:
    delete:
      tags:
        - User Profile
      summary: Revoke a specific session
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Session revoked successfully
        '404':
          description: Session not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /users/me/sessions/revoke-all:
    post:
      tags:
        - User Profile
      summary: Revoke all sessions except current
      responses:
        '200':
          description: All other sessions revoked

  # Store & Marketplace
  /store/items:
    get:
      tags:
        - Store
      summary: Browse store items
      description: |
        Browse available store items. Authentication is optional but recommended
        for personalized results and pricing.
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
        - {}
      parameters:
        - name: category
          in: query
          schema:
            type: string
        - name: tags
          in: query
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: priceMin
          in: query
          schema:
            type: number
            format: float
        - name: priceMax
          in: query
          schema:
            type: number
            format: float
        - name: featured
          in: query
          schema:
            type: boolean
        - name: search
          in: query
          schema:
            type: string
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
            maximum: 100
      responses:
        '200':
          description: Store items retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/StoreItem'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
                  filters:
                    $ref: '#/components/schemas/StoreFilters'

  /store/items/{itemId}:
    get:
      tags:
        - Store
      summary: Get store item details
      description: |
        Get detailed information about a specific store item. Authentication is optional
        but recommended for personalized pricing and availability.
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
        - {}
      parameters:
        - name: itemId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Store item retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StoreItemDetailed'
        '404':
          description: Item not found

  /store/items/{itemId}/pricing:
    get:
      tags:
        - Store
      summary: Get item pricing options
      description: |
        Get pricing information for a store item including volume discounts and subscription plans.
        Authentication is optional but recommended for personalized pricing.
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
        - {}
      parameters:
        - name: itemId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: instances
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: duration
          in: query
          schema:
            type: string
            enum: [monthly, quarterly, annual]
            default: monthly
      responses:
        '200':
          description: Pricing options retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PricingResponse'

  # Cart & Checkout
  /cart:
    get:
      tags:
        - Cart
      summary: Get user cart
      responses:
        '200':
          description: Cart retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'

    post:
      tags:
        - Cart
      summary: Add item to cart
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddToCartRequest'
      responses:
        '200':
          description: Item added to cart
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'
        '400':
          $ref: '#/components/responses/BadRequest'

    delete:
      tags:
        - Cart
      summary: Clear cart
      responses:
        '200':
          description: Cart cleared

  /cart/items/{cartItemId}:
    put:
      tags:
        - Cart
      summary: Update cart item
      parameters:
        - name: cartItemId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCartItemRequest'
      responses:
        '200':
          description: Cart item updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'

    delete:
      tags:
        - Cart
      summary: Remove item from cart
      parameters:
        - name: cartItemId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Item removed from cart
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'

  /features/{featureKey}/check:
    get:
      tags:
        - Features
      summary: Check feature access
      parameters:
        - name: featureKey
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Feature access status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureAccessCheck'

  # Admin Endpoints
  /admin/users:
    get:
      tags:
        - Administration
      summary: List all users
      description: |
        Retrieve a paginated list of all users in the system. Requires admin privileges.
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
            maximum: 100
        - name: search
          in: query
          schema:
            type: string
          description: Search users by email, name, or ID
        - name: status
          in: query
          schema:
            type: string
            enum: [active, inactive, suspended]
          description: Filter by user status
        - name: role
          in: query
          schema:
            type: string
          description: Filter by role name
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  users:
                    type: array
                    items:
                      $ref: '#/components/schemas/AdminUserView'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

  /admin/users/{userId}:
    get:
      tags:
        - Administration
      summary: Get user details
      description: Get detailed information about a specific user
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: User details retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminUserView'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      tags:
        - Administration
      summary: Update user
      description: Update user information and status
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminUpdateUserRequest'
      responses:
        '200':
          description: User updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminUserView'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

    delete:
      tags:
        - Administration
      summary: Delete user
      description: Permanently delete a user account
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: User deleted successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  /admin/users/{userId}/roles:
    get:
      tags:
        - Administration
      summary: Get user roles
      description: Get all roles assigned to a specific user
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: User roles retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  roles:
                    type: array
                    items:
                      $ref: '#/components/schemas/Role'

    post:
      tags:
        - Administration
      summary: Assign role to user
      description: Assign a role to a user
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                roleId:
                  type: string
                  format: uuid
              required:
                - roleId
      responses:
        '200':
          description: Role assigned successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  /admin/users/{userId}/roles/{roleId}:
    delete:
      tags:
        - Administration
      summary: Remove role from user
      description: Remove a role assignment from a user
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: roleId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Role removed successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  # Role Management
  /admin/roles:
    get:
      tags:
        - Administration
      summary: List all roles
      description: Retrieve all roles in the system
      parameters:
        - name: includeSystemRoles
          in: query
          schema:
            type: boolean
            default: true
          description: Include system-defined roles in the response
      responses:
        '200':
          description: Roles retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  roles:
                    type: array
                    items:
                      $ref: '#/components/schemas/Role'

    post:
      tags:
        - Administration
      summary: Create new role
      description: Create a new custom role
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateRoleRequest'
      responses:
        '201':
          description: Role created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

  /admin/roles/{roleId}:
    get:
      tags:
        - Administration
      summary: Get role details
      description: Get detailed information about a specific role
      parameters:
        - name: roleId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Role details retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      tags:
        - Administration
      summary: Update role
      description: Update role information and permissions
      parameters:
        - name: roleId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateRoleRequest'
      responses:
        '200':
          description: Role updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

    delete:
      tags:
        - Administration
      summary: Delete role
      description: Delete a custom role (system roles cannot be deleted)
      parameters:
        - name: roleId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Role deleted successfully
        '400':
          description: Cannot delete system role or role in use
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  # Permission Management
  /admin/permissions:
    get:
      tags:
        - Administration
      summary: List all permissions
      description: Retrieve all available permissions in the system
      parameters:
        - name: resource
          in: query
          schema:
            type: string
          description: Filter by resource type
        - name: action
          in: query
          schema:
            type: string
          description: Filter by action type
      responses:
        '200':
          description: Permissions retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  permissions:
                    type: array
                    items:
                      $ref: '#/components/schemas/Permission'
                  groupedByResource:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        $ref: '#/components/schemas/Permission'

    post:
      tags:
        - Administration
      summary: Create new permission
      description: Create a new custom permission
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePermissionRequest'
      responses:
        '201':
          description: Permission created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Permission'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

  /admin/permissions/{permissionId}:
    get:
      tags:
        - Administration
      summary: Get permission details
      description: Get detailed information about a specific permission
      parameters:
        - name: permissionId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Permission details retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Permission'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      tags:
        - Administration
      summary: Update permission
      description: Update permission information
      parameters:
        - name: permissionId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePermissionRequest'
      responses:
        '200':
          description: Permission updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Permission'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

    delete:
      tags:
        - Administration
      summary: Delete permission
      description: Delete a custom permission (system permissions cannot be deleted)
      parameters:
        - name: permissionId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Permission deleted successfully
        '400':
          description: Cannot delete system permission or permission in use
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  # Developer & Gitea Integration
  /developers/gitea/profile:
    get:
      tags:
        - Developer Integration
      summary: Get Gitea integration status
      description: |
        Get current Gitea integration status and profile information.
        Gitea access is automatically provisioned when user is promoted to Developer.
      responses:
        '200':
          description: Gitea integration status retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GiteaProfile'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          description: User does not have Developer role
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /developers/gitea/sync:
    post:
      tags:
        - Developer Integration
      summary: Sync with Gitea
      description: |
        Manually trigger a full sync between RSGlider and Gitea.
        Updates profile information and repository list.
      responses:
        '200':
          description: Sync completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  syncedAt:
                    type: string
                    format: date-time
                  repositoriesFound:
                    type: integer
                  repositoriesUpdated:
                    type: integer
                  profileUpdated:
                    type: boolean
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          description: User does not have Developer role or Gitea access
        '500':
          description: Sync failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /developers/repositories:
    get:
      tags:
        - Developer Integration
      summary: List developer repositories
      description: |
        Get all repositories from the developer's connected Gitea account.
        Shows both published and unpublished repositories.
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
            maximum: 100
        - name: visibility
          in: query
          schema:
            type: string
            enum: [public, private, all]
            default: all
        - name: published
          in: query
          schema:
            type: boolean
          description: Filter by marketplace publication status
      responses:
        '200':
          description: Repositories retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  repositories:
                    type: array
                    items:
                      $ref: '#/components/schemas/DeveloperRepository'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          description: No Gitea integration or insufficient permissions

  /developers/repositories/{repoId}/publish:
    post:
      tags:
        - Developer Integration
      summary: Publish repository to marketplace
      description: |
        Publish a Gitea repository as a marketplace item.
        Repository must contain proper metadata and pricing information.
      parameters:
        - name: repoId
          in: path
          required: true
          schema:
            type: string
          description: Gitea repository ID or full name (owner/repo)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishRepositoryRequest'
      responses:
        '201':
          description: Repository published to marketplace successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketplaceItem'
        '400':
          description: Invalid repository or missing required metadata
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          description: Repository not found

  /developers/repositories/{repoId}/unpublish:
    post:
      tags:
        - Developer Integration
      summary: Unpublish repository from marketplace
      description: Remove a repository from the marketplace (existing purchases remain valid)
      parameters:
        - name: repoId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Repository unpublished successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          description: Repository not found or not published

  /developers/repositories/{repoId}/sync:
    post:
      tags:
        - Developer Integration
      summary: Sync repository with marketplace
      description: |
        Manually sync repository changes with the marketplace item.
        Updates metadata, pricing, and content from the latest repository state.
      parameters:
        - name: repoId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Repository synced successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketplaceItem'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          description: Repository not found or not published

  # Developer Analytics & Revenue
  /developers/analytics/overview:
    get:
      tags:
        - Developer Integration
      summary: Get developer analytics overview
      description: Get comprehensive analytics for all developer's marketplace items
      parameters:
        - name: period
          in: query
          schema:
            type: string
            enum: [7d, 30d, 90d, 1y, all]
            default: 30d
          description: Analytics time period
      responses:
        '200':
          description: Analytics overview retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeveloperAnalytics'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /developers/analytics/repositories/{repoId}:
    get:
      tags:
        - Developer Integration
      summary: Get repository-specific analytics
      description: Get detailed analytics for a specific repository/marketplace item
      parameters:
        - name: repoId
          in: path
          required: true
          schema:
            type: string
        - name: period
          in: query
          schema:
            type: string
            enum: [7d, 30d, 90d, 1y, all]
            default: 30d
      responses:
        '200':
          description: Repository analytics retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RepositoryAnalytics'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          description: Repository not found

  /developers/revenue/summary:
    get:
      tags:
        - Developer Integration
      summary: Get revenue summary
      description: Get revenue summary and payout information
      parameters:
        - name: period
          in: query
          schema:
            type: string
            enum: [7d, 30d, 90d, 1y, all]
            default: 30d
      responses:
        '200':
          description: Revenue summary retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RevenueSummary'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /developers/revenue/payouts:
    get:
      tags:
        - Developer Integration
      summary: Get payout history
      description: Get history of developer payouts
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
            maximum: 100
        - name: status
          in: query
          schema:
            type: string
            enum: [pending, processing, completed, failed]
      responses:
        '200':
          description: Payout history retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  payouts:
                    type: array
                    items:
                      $ref: '#/components/schemas/DeveloperPayout'
                  pagination:
                    $ref: '#/components/schemas/Pagination'

  # Webhook Endpoints (for Gitea integration)
  /webhooks/gitea/repository:
    post:
      tags:
        - Developer Integration
      summary: Handle Gitea repository webhooks
      description: |
        Handle webhooks from Gitea for repository events.
        This endpoint is called by Gitea when repositories are updated.
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GiteaWebhookPayload'
      responses:
        '200':
          description: Webhook processed successfully
        '400':
          description: Invalid webhook payload
        '401':
          description: Invalid webhook signature

  # Admin endpoints for developer management
  /admin/developers:
    get:
      tags:
        - Administration
      summary: List all developers
      description: Get list of all users with Developer role and their Gitea integration status
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
            maximum: 100
        - name: giteaConnected
          in: query
          schema:
            type: boolean
          description: Filter by Gitea connection status
      responses:
        '200':
          description: Developers retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  developers:
                    type: array
                    items:
                      $ref: '#/components/schemas/AdminDeveloperView'
                  pagination:
                    $ref: '#/components/schemas/Pagination'

  /admin/developers/{userId}/promote:
    post:
      tags:
        - Administration
      summary: Promote user to developer with Gitea provisioning
      description: |
        Promote a regular user to Developer role and automatically provision Gitea access.
        This will:
        1. Assign Developer role to the user
        2. Create/link Gitea account with same credentials
        3. Set up automatic sync between RSGlider and Gitea
        4. Grant marketplace publishing capabilities
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: Reason for promotion (for audit trail)
                notifyUser:
                  type: boolean
                  default: true
                  description: Whether to send notification to user
                giteaUsername:
                  type: string
                  description: |
                    Optional custom Gitea username. If not provided, will use RSGlider username.
                    Must be unique in Gitea.
                giteaEmail:
                  type: string
                  format: email
                  description: |
                    Optional custom Gitea email. If not provided, will use RSGlider email.
                initialRepositories:
                  type: array
                  items:
                    type: string
                  description: Optional list of repository names to create initially
              required:
                - reason
      responses:
        '200':
          description: User promoted to developer and Gitea account provisioned successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/AdminDeveloperView'
                  - type: object
                    properties:
                      giteaProvisioning:
                        type: object
                        properties:
                          success:
                            type: boolean
                          giteaUserId:
                            type: integer
                          giteaUsername:
                            type: string
                          giteaUrl:
                            type: string
                            format: uri
                          initialSync:
                            type: boolean
                          repositoriesCreated:
                            type: array
                            items:
                              type: string
        '400':
          description: User already has Developer role or Gitea provisioning failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  /admin/developers/{userId}/revoke:
    post:
      tags:
        - Administration
      summary: Revoke developer access and Gitea account
      description: |
        Revoke Developer role from user and handle Gitea account management.
        This will:
        1. Remove Developer role from user
        2. Unpublish all marketplace items
        3. Optionally disable/delete Gitea account
        4. Archive or transfer repositories
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: Reason for revocation (for audit trail)
                notifyUser:
                  type: boolean
                  default: true
                giteaAction:
                  type: string
                  enum: [disable, delete, archive, transfer]
                  default: disable
                  description: |
                    Action to take on Gitea account:
                    - disable: Disable account but keep repositories
                    - delete: Delete account and repositories
                    - archive: Archive repositories and disable account
                    - transfer: Transfer repositories to admin account
                transferToUser:
                  type: string
                  description: Username to transfer repositories to (required if giteaAction=transfer)
                preserveMarketplaceData:
                  type: boolean
                  default: true
                  description: Whether to preserve marketplace analytics and revenue data
              required:
                - reason
      responses:
        '200':
          description: Developer access revoked and Gitea account handled successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  revoked:
                    type: boolean
                    enum: [true]
                  giteaAction:
                    type: string
                  repositoriesAffected:
                    type: integer
                  marketplaceItemsUnpublished:
                    type: integer
                  dataPreserved:
                    type: boolean
        '400':
          description: Invalid revocation parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  # Admin Gitea Management
  /admin/gitea/sync-all:
    post:
      tags:
        - Administration
      summary: Sync all developers with Gitea
      description: |
        Trigger a full sync of all developers with their Gitea accounts.
        Updates profiles, repositories, and marketplace items.
      responses:
        '200':
          description: Sync initiated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  syncId:
                    type: string
                    format: uuid
                  developersToSync:
                    type: integer
                  estimatedDuration:
                    type: string
                    description: Estimated completion time
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

  /admin/gitea/sync-status/{syncId}:
    get:
      tags:
        - Administration
      summary: Get sync status
      description: Get the status of a bulk sync operation
      parameters:
        - name: syncId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Sync status retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  syncId:
                    type: string
                    format: uuid
                  status:
                    type: string
                    enum: [pending, running, completed, failed]
                  progress:
                    type: object
                    properties:
                      total:
                        type: integer
                      completed:
                        type: integer
                      failed:
                        type: integer
                      percentage:
                        type: number
                        format: float
                  startedAt:
                    type: string
                    format: date-time
                  completedAt:
                    type: string
                    format: date-time
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        userId:
                          type: string
                          format: uuid
                        username:
                          type: string
                        error:
                          type: string

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key

  schemas:
    # Authentication Schemas
    RegisterRequest:
      type: object
      required:
        - email
        - password
        - firstName
        - lastName
      properties:
        email:
          type: string
          format: email
        password:
          type: string
          minLength: 8
          pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]'
        firstName:
          type: string
          minLength: 1
          maxLength: 50
        lastName:
          type: string
          minLength: 1
          maxLength: 50
        acceptTerms:
          type: boolean
          enum: [true]

    LoginRequest:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
        password:
          type: string
        twoFactorCode:
          type: string
          pattern: '^[0-9]{6}$'
        rememberMe:
          type: boolean
          default: false

    AuthResponse:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/User'
        accessToken:
          type: string
        refreshToken:
          type: string
        expiresIn:
          type: integer
        requiresTwoFactor:
          type: boolean

    TokenResponse:
      type: object
      properties:
        accessToken:
          type: string
          description: JWT access token (typically expires in 15-30 minutes)
        refreshToken:
          type: string
          description: Refresh token for obtaining new access tokens (rotated on each use)
        expiresIn:
          type: integer
          description: Access token expiration time in seconds
        tokenType:
          type: string
          enum: [Bearer]
          default: Bearer

    # User Schemas
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        firstName:
          type: string
        lastName:
          type: string
        avatar:
          type: string
          format: uri
        roles:
          type: array
          items:
            $ref: '#/components/schemas/Role'
        twoFactorEnabled:
          type: boolean
        emailVerified:
          type: boolean
        status:
          type: string
          enum: [active, inactive, suspended]
        lastLoginAt:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    UpdateUserRequest:
      type: object
      properties:
        firstName:
          type: string
          minLength: 1
          maxLength: 50
        lastName:
          type: string
          minLength: 1
          maxLength: 50
        avatar:
          type: string
          format: uri

    # Role Schemas
    Role:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
        permissions:
          type: array
          items:
            $ref: '#/components/schemas/Permission'
        isSystemRole:
          type: boolean
        userCount:
          type: integer
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    # Permission Schemas
    Permission:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        resource:
          type: string
        action:
          type: string
        description:
          type: string
        isSystemPermission:
          type: boolean
        createdAt:
          type: string
          format: date-time

    # Admin Schemas
    AdminUserView:
      allOf:
        - $ref: '#/components/schemas/User'
        - type: object
          properties:
            loginHistory:
              type: array
              items:
                type: object
                properties:
                  timestamp:
                    type: string
                    format: date-time
                  ipAddress:
                    type: string
                  userAgent:
                    type: string
                  success:
                    type: boolean
            sessionCount:
              type: integer
              description: Number of active sessions
            totalLogins:
              type: integer
              description: Total number of logins
            failedLoginAttempts:
              type: integer
              description: Recent failed login attempts

    AdminUpdateUserRequest:
      type: object
      properties:
        firstName:
          type: string
          minLength: 1
          maxLength: 50
        lastName:
          type: string
          minLength: 1
          maxLength: 50
        email:
          type: string
          format: email
        status:
          type: string
          enum: [active, inactive, suspended]
        emailVerified:
          type: boolean
        twoFactorEnabled:
          type: boolean
        notes:
          type: string
          description: Admin notes about the user

    CreateRoleRequest:
      type: object
      required:
        - name
        - description
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 50
          pattern: '^[a-zA-Z0-9_-]+$'
        description:
          type: string
          minLength: 1
          maxLength: 255
        permissions:
          type: array
          items:
            type: string
            format: uuid
          description: Array of permission IDs to assign to this role

    UpdateRoleRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 50
          pattern: '^[a-zA-Z0-9_-]+$'
        description:
          type: string
          minLength: 1
          maxLength: 255
        permissions:
          type: array
          items:
            type: string
            format: uuid
          description: Array of permission IDs to assign to this role

    CreatePermissionRequest:
      type: object
      required:
        - name
        - resource
        - action
        - description
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 50
          pattern: '^[a-zA-Z0-9_:-]+$'
        resource:
          type: string
          minLength: 1
          maxLength: 50
          description: The resource this permission applies to (e.g., 'users', 'roles', 'store')
        action:
          type: string
          minLength: 1
          maxLength: 50
          description: The action this permission allows (e.g., 'read', 'write', 'delete')
        description:
          type: string
          minLength: 1
          maxLength: 255

    UpdatePermissionRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 50
          pattern: '^[a-zA-Z0-9_:-]+$'
        resource:
          type: string
          minLength: 1
          maxLength: 50
        action:
          type: string
          minLength: 1
          maxLength: 50
        description:
          type: string
          minLength: 1
          maxLength: 255

    # Gitea Integration Schemas
    GiteaProfile:
      type: object
      properties:
        id:
          type: integer
          description: Gitea user ID
        username:
          type: string
          description: Gitea username
        fullName:
          type: string
          description: User's full name in Gitea
        email:
          type: string
          format: email
        avatarUrl:
          type: string
          format: uri
        profileUrl:
          type: string
          format: uri
          description: Direct link to git.rsglider.com profile
        publicRepos:
          type: integer
        privateRepos:
          type: integer
        totalRepos:
          type: integer
        followers:
          type: integer
        following:
          type: integer
        provisionedAt:
          type: string
          format: date-time
          description: When the Gitea account was provisioned
        lastSyncAt:
          type: string
          format: date-time
        syncStatus:
          type: string
          enum: [active, syncing, error, disabled]
        accountStatus:
          type: string
          enum: [active, disabled, archived]
        ssoEnabled:
          type: boolean
          description: Whether SSO login is enabled
        adminProvisioned:
          type: boolean
          description: Whether account was provisioned by admin
        provisionedBy:
          type: string
          description: Admin username who provisioned the account

    DeveloperRepository:
      type: object
      properties:
        id:
          type: integer
          description: Gitea repository ID
        name:
          type: string
          description: Repository name
        fullName:
          type: string
          description: Full repository name (owner/repo)
        description:
          type: string
        private:
          type: boolean
        fork:
          type: boolean
        size:
          type: integer
          description: Repository size in KB
        language:
          type: string
          description: Primary programming language
        stars:
          type: integer
        forks:
          type: integer
        openIssues:
          type: integer
        defaultBranch:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        pushedAt:
          type: string
          format: date-time
        cloneUrl:
          type: string
          format: uri
        sshUrl:
          type: string
        htmlUrl:
          type: string
          format: uri
        isPublished:
          type: boolean
          description: Whether this repo is published to marketplace
        marketplaceItemId:
          type: string
          format: uuid
          description: Associated marketplace item ID if published
        hasMarketplaceMetadata:
          type: boolean
          description: Whether repo contains required marketplace metadata

    PublishRepositoryRequest:
      type: object
      required:
        - category
        - pricing
      properties:
        category:
          type: string
          description: Marketplace category for the item
        tags:
          type: array
          items:
            type: string
          description: Tags for better discoverability
        pricing:
          $ref: '#/components/schemas/RepositoryPricing'
        visibility:
          type: string
          enum: [public, private]
          default: public
          description: Marketplace visibility
        featured:
          type: boolean
          default: false
          description: Request featured placement (admin approval required)
        customDescription:
          type: string
          description: Override repository description for marketplace
        customName:
          type: string
          description: Override repository name for marketplace

    RepositoryPricing:
      type: object
      required:
        - type
      properties:
        type:
          type: string
          enum: [free, one_time, subscription, pay_what_you_want]
        basePrice:
          type: number
          format: float
          minimum: 0
        currency:
          type: string
          enum: [USD, EUR, GBP]
          default: USD
        subscriptionPlans:
          type: array
          items:
            $ref: '#/components/schemas/SubscriptionPlan'
        minimumPrice:
          type: number
          format: float
          minimum: 0
          description: For pay_what_you_want pricing
        suggestedPrice:
          type: number
          format: float
          description: For pay_what_you_want pricing

    MarketplaceItem:
      allOf:
        - $ref: '#/components/schemas/StoreItem'
        - type: object
          properties:
            repositoryId:
              type: integer
              description: Associated Gitea repository ID
            repositoryUrl:
              type: string
              format: uri
            developerId:
              type: string
              format: uuid
            developerName:
              type: string
            developerAvatar:
              type: string
              format: uri
            sourceType:
              type: string
              enum: [gitea_repository]
            lastSyncAt:
              type: string
              format: date-time
            version:
              type: string
              description: Current version/tag from repository
            downloadCount:
              type: integer
            revenue:
              type: number
              format: float
              description: Total revenue generated
            revenueShare:
              type: object
              properties:
                developerPercentage:
                  type: number
                  format: float
                platformPercentage:
                  type: number
                  format: float

    # Analytics & Revenue Schemas
    DeveloperAnalytics:
      type: object
      properties:
        period:
          type: string
          enum: [7d, 30d, 90d, 1y, all]
        totalRevenue:
          type: number
          format: float
        totalDownloads:
          type: integer
        totalViews:
          type: integer
        activeItems:
          type: integer
          description: Number of published marketplace items
        topPerformingItems:
          type: array
          items:
            $ref: '#/components/schemas/ItemPerformance'
        revenueByPeriod:
          type: array
          items:
            type: object
            properties:
              date:
                type: string
                format: date
              revenue:
                type: number
                format: float
              downloads:
                type: integer
        categoryBreakdown:
          type: object
          additionalProperties:
            type: object
            properties:
              revenue:
                type: number
                format: float
              downloads:
                type: integer
              items:
                type: integer

    RepositoryAnalytics:
      type: object
      properties:
        repositoryId:
          type: string
        period:
          type: string
        revenue:
          type: number
          format: float
        downloads:
          type: integer
        views:
          type: integer
        uniqueUsers:
          type: integer
        conversionRate:
          type: number
          format: float
          description: Views to downloads conversion rate
        rating:
          type: number
          format: float
        reviewCount:
          type: integer
        downloadsByPeriod:
          type: array
          items:
            type: object
            properties:
              date:
                type: string
                format: date
              downloads:
                type: integer
              revenue:
                type: number
                format: float
        geographicBreakdown:
          type: object
          additionalProperties:
            type: integer
        referralSources:
          type: object
          additionalProperties:
            type: integer

    ItemPerformance:
      type: object
      properties:
        itemId:
          type: string
          format: uuid
        name:
          type: string
        revenue:
          type: number
          format: float
        downloads:
          type: integer
        views:
          type: integer
        conversionRate:
          type: number
          format: float

    RevenueSummary:
      type: object
      properties:
        period:
          type: string
        totalRevenue:
          type: number
          format: float
        platformFee:
          type: number
          format: float
        netRevenue:
          type: number
          format: float
        pendingPayout:
          type: number
          format: float
        nextPayoutDate:
          type: string
          format: date
        revenueShare:
          type: object
          properties:
            developerPercentage:
              type: number
              format: float
            platformPercentage:
              type: number
              format: float
        payoutThreshold:
          type: number
          format: float
        currency:
          type: string
          enum: [USD, EUR, GBP]

    DeveloperPayout:
      type: object
      properties:
        id:
          type: string
          format: uuid
        amount:
          type: number
          format: float
        currency:
          type: string
          enum: [USD, EUR, GBP]
        status:
          type: string
          enum: [pending, processing, completed, failed]
        paymentMethod:
          type: string
          enum: [btcpay_bitcoin]
        transactionId:
          type: string
        period:
          type: object
          properties:
            startDate:
              type: string
              format: date
            endDate:
              type: string
              format: date
        itemsIncluded:
          type: array
          items:
            type: object
            properties:
              itemId:
                type: string
                format: uuid
              itemName:
                type: string
              revenue:
                type: number
                format: float
              downloads:
                type: integer
        createdAt:
          type: string
          format: date-time
        processedAt:
          type: string
          format: date-time
        failureReason:
          type: string

    # Webhook Schemas
    GiteaWebhookPayload:
      type: object
      properties:
        action:
          type: string
          enum: [created, deleted, pushed, released]
        repository:
          type: object
          properties:
            id:
              type: integer
            name:
              type: string
            full_name:
              type: string
            private:
              type: boolean
            owner:
              type: object
              properties:
                id:
                  type: integer
                username:
                  type: string
                email:
                  type: string
        pusher:
          type: object
          properties:
            id:
              type: integer
            username:
              type: string
            email:
              type: string
        commits:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
              message:
                type: string
              url:
                type: string
              author:
                type: object
                properties:
                  name:
                    type: string
                  email:
                    type: string
        release:
          type: object
          properties:
            id:
              type: integer
            tag_name:
              type: string
            name:
              type: string
            body:
              type: string
            draft:
              type: boolean
            prerelease:
              type: boolean

    AdminDeveloperView:
      allOf:
        - $ref: '#/components/schemas/AdminUserView'
        - type: object
          properties:
            giteaIntegration:
              type: object
              properties:
                connected:
                  type: boolean
                giteaUserId:
                  type: integer
                giteaUsername:
                  type: string
                connectedAt:
                  type: string
                  format: date-time
                lastSyncAt:
                  type: string
                  format: date-time
            marketplaceStats:
              type: object
              properties:
                publishedItems:
                  type: integer
                totalRevenue:
                  type: number
                  format: float
                totalDownloads:
                  type: integer
                averageRating:
                  type: number
                  format: float
            developerSince:
              type: string
              format: date-time
            promotedBy:
              type: string
              description: Admin who promoted this user
            promotionReason:
              type: string

    # Session Schema
    Session:
      type: object
      properties:
        id:
          type: string
          format: uuid
        deviceInfo:
          type: string
        ipAddress:
          type: string
          format: ipv4
        location:
          type: string
        isCurrent:
          type: boolean
        createdAt:
          type: string
          format: date-time
        lastActiveAt:
          type: string
          format: date-time
        expiresAt:
          type: string
          format: date-time

    # Two-Factor Authentication Schemas
    TwoFactorSetupResponse:
      type: object
      properties:
        secret:
          type: string
          description: Base32-encoded TOTP secret for manual entry
        qrCodeUrl:
          type: string
          format: uri
          description: Data URL for QR code image
        backupCodes:
          type: array
          items:
            type: string
          description: One-time backup codes (only shown during setup)
        manualEntryKey:
          type: string
          description: Human-readable secret key for manual entry
        issuer:
          type: string
          description: Service name for the authenticator app
          example: "RSGlider"

    TwoFactorVerifyRequest:
      type: object
      required:
        - code
      properties:
        code:
          type: string
          pattern: '^[0-9]{6}$'
          description: 6-digit TOTP code from authenticator app

    TwoFactorEnabledResponse:
      type: object
      properties:
        enabled:
          type: boolean
          enum: [true]
        backupCodes:
          type: array
          items:
            type: string
          description: One-time backup codes for account recovery
        message:
          type: string
          example: "Two-factor authentication has been successfully enabled"

    TwoFactorDisableRequest:
      type: object
      required:
        - password
      properties:
        password:
          type: string
          description: Current user password for verification
        code:
          type: string
          description: Either 6-digit TOTP code or backup code
          oneOf:
            - pattern: '^[0-9]{6}$'
              description: TOTP code from authenticator app
            - pattern: '^[A-Z0-9]{8}$'
              description: 8-character backup code

    BackupCodesResponse:
      type: object
      properties:
        codes:
          type: array
          items:
            type: string
            pattern: '^[A-Z0-9]{8}$'
          description: List of 8-character backup codes
        generatedAt:
          type: string
          format: date-time
          description: When these codes were generated
        usedCodes:
          type: array
          items:
            type: string
          description: List of already used backup codes (masked)
        remainingCodes:
          type: integer
          description: Number of unused backup codes

    # Store & E-commerce Schemas
    StoreItem:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
        shortDescription:
          type: string
        category:
          type: string
        tags:
          type: array
          items:
            type: string
        images:
          type: array
          items:
            type: string
            format: uri
        featured:
          type: boolean
        rating:
          type: number
          format: float
          minimum: 0
          maximum: 5
        reviewCount:
          type: integer
        basePrice:
          type: number
          format: float
        currency:
          type: string
          enum: [USD, EUR, GBP]
          default: USD
        isFree:
          type: boolean
        hasInstanceLimits:
          type: boolean
        featureFlags:
          type: array
          items:
            type: string
        status:
          type: string
          enum: [published, draft, archived]
        createdAt:
          type: string
          format: date-time

    StoreItemDetailed:
      allOf:
        - $ref: '#/components/schemas/StoreItem'
        - type: object
          properties:
            fullDescription:
              type: string
            features:
              type: array
              items:
                type: string
            requirements:
              type: object
              properties:
                system:
                  type: array
                  items:
                    type: string
                technical:
                  type: array
                  items:
                    type: string
            pricing:
              $ref: '#/components/schemas/PricingStructure'

    PricingStructure:
      type: object
      properties:
        basePrice:
          type: number
          format: float
        currency:
          type: string
          enum: [USD, EUR, GBP]
        isFree:
          type: boolean
        subscriptionPlans:
          type: array
          items:
            $ref: '#/components/schemas/SubscriptionPlan'
        instancePricing:
          type: array
          items:
            $ref: '#/components/schemas/InstancePricingTier'
        volumeDiscounts:
          type: array
          items:
            $ref: '#/components/schemas/VolumeDiscount'

    SubscriptionPlan:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        duration:
          type: string
          enum: [monthly, quarterly, annual]
        price:
          type: number
          format: float
        originalPrice:
          type: number
          format: float
        discountPercentage:
          type: number
          format: float
        maxInstances:
          type: integer
        features:
          type: array
          items:
            type: string
        popular:
          type: boolean

    InstancePricingTier:
      type: object
      properties:
        minInstances:
          type: integer
        maxInstances:
          type: integer
        pricePerInstance:
          type: number
          format: float
        totalPrice:
          type: number
          format: float
        discountPercentage:
          type: number
          format: float

    VolumeDiscount:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        conditions:
          type: object
          properties:
            minQuantity:
              type: integer
            minInstances:
              type: integer
            duration:
              type: string
              enum: [monthly, quarterly, annual]
            bundleItems:
              type: array
              items:
                type: string
                format: uuid
        discountType:
          type: string
          enum: [percentage, fixed_amount, buy_x_get_y]
        discountValue:
          type: number
          format: float
        maxDiscount:
          type: number
          format: float
        validFrom:
          type: string
          format: date-time
        validUntil:
          type: string
          format: date-time

    PricingResponse:
      type: object
      properties:
        itemId:
          type: string
          format: uuid
        requestedInstances:
          type: integer
        requestedDuration:
          type: string
          enum: [monthly, quarterly, annual]
        options:
          type: array
          items:
            $ref: '#/components/schemas/PricingOption'
        appliedDiscounts:
          type: array
          items:
            $ref: '#/components/schemas/AppliedDiscount'

    PricingOption:
      type: object
      properties:
        planId:
          type: string
          format: uuid
        name:
          type: string
        instances:
          type: integer
        duration:
          type: string
          enum: [monthly, quarterly, annual]
        originalPrice:
          type: number
          format: float
        finalPrice:
          type: number
          format: float
        savings:
          type: number
          format: float
        savingsPercentage:
          type: number
          format: float
        priceBreakdown:
          type: object
          properties:
            basePrice:
              type: number
              format: float
            instanceCost:
              type: number
              format: float
            discounts:
              type: number
              format: float
            taxes:
              type: number
              format: float

    AppliedDiscount:
      type: object
      properties:
        name:
          type: string
        type:
          type: string
          enum: [volume, duration, bundle, coupon]
        amount:
          type: number
          format: float
        percentage:
          type: number
          format: float

    # Cart Schemas
    Cart:
      type: object
      properties:
        id:
          type: string
          format: uuid
        items:
          type: array
          items:
            $ref: '#/components/schemas/CartItem'
        subtotal:
          type: number
          format: float
        discounts:
          type: number
          format: float
        taxes:
          type: number
          format: float
        total:
          type: number
          format: float
        currency:
          type: string
          enum: [USD, EUR, GBP]
        appliedCoupons:
          type: array
          items:
            $ref: '#/components/schemas/AppliedCoupon'
        estimatedSavings:
          type: number
          format: float

    CartItem:
      type: object
      properties:
        id:
          type: string
          format: uuid
        itemId:
          type: string
          format: uuid
        itemName:
          type: string
        planId:
          type: string
          format: uuid
        planName:
          type: string
        instances:
          type: integer
        duration:
          type: string
          enum: [monthly, quarterly, annual]
        unitPrice:
          type: number
          format: float
        totalPrice:
          type: number
          format: float
        discounts:
          type: array
          items:
            $ref: '#/components/schemas/AppliedDiscount'
        addedAt:
          type: string
          format: date-time

    AddToCartRequest:
      type: object
      required:
        - itemId
        - planId
      properties:
        itemId:
          type: string
          format: uuid
        planId:
          type: string
          format: uuid
        instances:
          type: integer
          minimum: 1
          default: 1
        duration:
          type: string
          enum: [monthly, quarterly, annual]
          default: monthly

    UpdateCartItemRequest:
      type: object
      properties:
        instances:
          type: integer
          minimum: 1
        duration:
          type: string
          enum: [monthly, quarterly, annual]

    AppliedCoupon:
      type: object
      properties:
        code:
          type: string
        name:
          type: string
        discountAmount:
          type: number
          format: float
        discountType:
          type: string
          enum: [percentage, fixed_amount]

    # Feature Flags & Access Control
    FeatureAccessCheck:
      type: object
      properties:
        featureKey:
          type: string
        hasAccess:
          type: boolean
        reason:
          type: string
          enum: [granted, no_subscription, expired, instance_limit_exceeded, feature_disabled]
        maxInstances:
          type: integer
        availableInstances:
          type: integer
        nextAvailableAt:
          type: string
          format: date-time
        upgradeOptions:
          type: array
          items:
            $ref: '#/components/schemas/UpgradeOption'

    UpgradeOption:
      type: object
      properties:
        planId:
          type: string
          format: uuid
        planName:
          type: string
        additionalInstances:
          type: integer
        price:
          type: number
          format: float
        duration:
          type: string

    # Utility Schemas
    Pagination:
      type: object
      properties:
        page:
          type: integer
        limit:
          type: integer
        total:
          type: integer
        totalPages:
          type: integer
        hasNext:
          type: boolean
        hasPrev:
          type: boolean

    StoreFilters:
      type: object
      properties:
        categories:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              count:
                type: integer
        priceRanges:
          type: array
          items:
            type: object
            properties:
              min:
                type: number
              max:
                type: number
              count:
                type: integer
        tags:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              count:
                type: integer

    Error:
      type: object
      properties:
        error:
          type: string
        message:
          type: string
        code:
          type: string
        details:
          type: object

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

tags:
  - name: Authentication
    description: User authentication and account management
  - name: User Profile
    description: User profile management, 2FA setup, and session security
  - name: Store
    description: Browse and view store items
  - name: Cart
    description: Shopping cart management
  - name: Features
    description: Feature flags and instance management
  - name: Administration
    description: Admin endpoints for user, role, and permission management
  - name: Developer Integration
    description: Gitea OAuth integration, repository management, and marketplace publishing