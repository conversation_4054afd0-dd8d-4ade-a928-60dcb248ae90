openapi: 3.0.3
info:
  title: RSGlider API
  description: |
    A comprehensive user management API with role-based access control, 2FA support,
    session management, e-commerce store, and administrative capabilities.
  version: 1.0.0
  contact:
    name: RSGlider Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.rsglider.com/v1
    description: Production server
  - url: https://staging-api.rsglider.com/v1
    description: Staging server
  - url: http://localhost:3000/v1
    description: Development server

security:
  - BearerAuth: []
  - ApiKeyAuth: []

paths:
  # Authentication Endpoints
  /auth/register:
    post:
      tags:
        - Authentication
      summary: Register a new user
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          description: User already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/login:
    post:
      tags:
        - Authentication
      summary: Login user
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '423':
          description: Account locked due to failed attempts
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/logout:
    post:
      tags:
        - Authentication
      summary: Logout user (invalidate current session)
      responses:
        '200':
          description: Logout successful
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/refresh:
    post:
      tags:
        - Authentication
      summary: Refresh access token using refresh token
      description: |
        Exchange a valid refresh token for a new access token. Implements refresh token rotation
        for enhanced security - the old refresh token is invalidated and a new one is provided.
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                refreshToken:
                  type: string
                  description: Valid refresh token from login or previous refresh
              required:
                - refreshToken
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/TokenResponse'
                  - type: object
                    properties:
                      sessionId:
                        type: string
                        format: uuid
                        description: Session ID for tracking purposes
        '401':
          description: Invalid or expired refresh token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '429':
          description: Too many refresh attempts
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/forgot-password:
    post:
      tags:
        - Authentication
      summary: Request password reset
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
              required:
                - email
      responses:
        '200':
          description: Password reset email sent
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/reset-password:
    post:
      tags:
        - Authentication
      summary: Reset password with token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                newPassword:
                  type: string
                  minLength: 8
              required:
                - token
                - newPassword
      responses:
        '200':
          description: Password reset successful
        '400':
          $ref: '#/components/responses/BadRequest'
        '410':
          description: Reset token expired
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'



  # User Profile Endpoints
  /users/me:
    get:
      tags:
        - User Profile
      summary: Get current user profile
      responses:
        '200':
          description: User profile retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          $ref: '#/components/responses/Unauthorized'

    put:
      tags:
        - User Profile
      summary: Update current user profile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /users/me/change-password:
    post:
      tags:
        - User Profile
      summary: Change user password
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                currentPassword:
                  type: string
                newPassword:
                  type: string
                  minLength: 8
              required:
                - currentPassword
                - newPassword
      responses:
        '200':
          description: Password changed successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # Session Management
  /users/me/sessions:
    get:
      tags:
        - Session Management
      summary: Get user active sessions across all devices
      description: |
        Retrieve all active sessions for the current user. Sessions are tracked server-side
        regardless of token type (JWT or traditional sessions) for security monitoring.
        Shows device information, location, and last activity.
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
            maximum: 100
      responses:
        '200':
          description: Sessions retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  sessions:
                    type: array
                    items:
                      $ref: '#/components/schemas/Session'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
                  currentSessionId:
                    type: string
                    format: uuid
                    description: ID of the current session

  /users/me/sessions/{sessionId}:
    delete:
      tags:
        - Session Management
      summary: Revoke a specific session
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Session revoked successfully
        '404':
          description: Session not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /users/me/sessions/revoke-all:
    post:
      tags:
        - Session Management
      summary: Revoke all sessions except current
      responses:
        '200':
          description: All other sessions revoked

  # Store & Marketplace
  /store/items:
    get:
      tags:
        - Store
      summary: Browse store items
      security: []
      parameters:
        - name: category
          in: query
          schema:
            type: string
        - name: tags
          in: query
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: priceMin
          in: query
          schema:
            type: number
            format: float
        - name: priceMax
          in: query
          schema:
            type: number
            format: float
        - name: featured
          in: query
          schema:
            type: boolean
        - name: search
          in: query
          schema:
            type: string
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
            maximum: 100
      responses:
        '200':
          description: Store items retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/StoreItem'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
                  filters:
                    $ref: '#/components/schemas/StoreFilters'

  /store/items/{itemId}:
    get:
      tags:
        - Store
      summary: Get store item details
      security: []
      parameters:
        - name: itemId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Store item retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StoreItemDetailed'
        '404':
          description: Item not found

  /store/items/{itemId}/pricing:
    get:
      tags:
        - Store
      summary: Get item pricing options
      security: []
      parameters:
        - name: itemId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: instances
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: duration
          in: query
          schema:
            type: string
            enum: [monthly, quarterly, annual]
            default: monthly
      responses:
        '200':
          description: Pricing options retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PricingResponse'

  # Cart & Checkout
  /cart:
    get:
      tags:
        - Cart
      summary: Get user cart
      responses:
        '200':
          description: Cart retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'

    post:
      tags:
        - Cart
      summary: Add item to cart
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddToCartRequest'
      responses:
        '200':
          description: Item added to cart
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'
        '400':
          $ref: '#/components/responses/BadRequest'

    delete:
      tags:
        - Cart
      summary: Clear cart
      responses:
        '200':
          description: Cart cleared

  /cart/items/{cartItemId}:
    put:
      tags:
        - Cart
      summary: Update cart item
      parameters:
        - name: cartItemId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCartItemRequest'
      responses:
        '200':
          description: Cart item updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'

    delete:
      tags:
        - Cart
      summary: Remove item from cart
      parameters:
        - name: cartItemId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Item removed from cart
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'

  /features/{featureKey}/check:
    get:
      tags:
        - Features
      summary: Check feature access
      parameters:
        - name: featureKey
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Feature access status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureAccessCheck'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key

  schemas:
    # Authentication Schemas
    RegisterRequest:
      type: object
      required:
        - email
        - password
        - firstName
        - lastName
      properties:
        email:
          type: string
          format: email
        password:
          type: string
          minLength: 8
          pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]'
        firstName:
          type: string
          minLength: 1
          maxLength: 50
        lastName:
          type: string
          minLength: 1
          maxLength: 50
        acceptTerms:
          type: boolean
          enum: [true]

    LoginRequest:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
        password:
          type: string
        twoFactorCode:
          type: string
          pattern: '^[0-9]{6}$'
        rememberMe:
          type: boolean
          default: false

    AuthResponse:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/User'
        accessToken:
          type: string
        refreshToken:
          type: string
        expiresIn:
          type: integer
        requiresTwoFactor:
          type: boolean

    TokenResponse:
      type: object
      properties:
        accessToken:
          type: string
          description: JWT access token (typically expires in 15-30 minutes)
        refreshToken:
          type: string
          description: Refresh token for obtaining new access tokens (rotated on each use)
        expiresIn:
          type: integer
          description: Access token expiration time in seconds
        tokenType:
          type: string
          enum: [Bearer]
          default: Bearer

    # User Schemas
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        firstName:
          type: string
        lastName:
          type: string
        avatar:
          type: string
          format: uri
        roles:
          type: array
          items:
            $ref: '#/components/schemas/Role'
        twoFactorEnabled:
          type: boolean
        emailVerified:
          type: boolean
        status:
          type: string
          enum: [active, inactive, suspended]
        lastLoginAt:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    UpdateUserRequest:
      type: object
      properties:
        firstName:
          type: string
          minLength: 1
          maxLength: 50
        lastName:
          type: string
          minLength: 1
          maxLength: 50
        avatar:
          type: string
          format: uri

    # Role Schemas
    Role:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
        permissions:
          type: array
          items:
            $ref: '#/components/schemas/Permission'
        isSystemRole:
          type: boolean
        userCount:
          type: integer
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    # Permission Schemas
    Permission:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        resource:
          type: string
        action:
          type: string
        description:
          type: string
        isSystemPermission:
          type: boolean
        createdAt:
          type: string
          format: date-time

    # Session Schema
    Session:
      type: object
      properties:
        id:
          type: string
          format: uuid
        deviceInfo:
          type: string
        ipAddress:
          type: string
          format: ipv4
        location:
          type: string
        isCurrent:
          type: boolean
        createdAt:
          type: string
          format: date-time
        lastActiveAt:
          type: string
          format: date-time
        expiresAt:
          type: string
          format: date-time

    # Store & E-commerce Schemas
    StoreItem:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
        shortDescription:
          type: string
        category:
          type: string
        tags:
          type: array
          items:
            type: string
        images:
          type: array
          items:
            type: string
            format: uri
        featured:
          type: boolean
        rating:
          type: number
          format: float
          minimum: 0
          maximum: 5
        reviewCount:
          type: integer
        basePrice:
          type: number
          format: float
        currency:
          type: string
          enum: [USD, EUR, GBP]
          default: USD
        isFree:
          type: boolean
        hasInstanceLimits:
          type: boolean
        featureFlags:
          type: array
          items:
            type: string
        status:
          type: string
          enum: [published, draft, archived]
        createdAt:
          type: string
          format: date-time

    StoreItemDetailed:
      allOf:
        - $ref: '#/components/schemas/StoreItem'
        - type: object
          properties:
            fullDescription:
              type: string
            features:
              type: array
              items:
                type: string
            requirements:
              type: object
              properties:
                system:
                  type: array
                  items:
                    type: string
                technical:
                  type: array
                  items:
                    type: string
            pricing:
              $ref: '#/components/schemas/PricingStructure'

    PricingStructure:
      type: object
      properties:
        basePrice:
          type: number
          format: float
        currency:
          type: string
          enum: [USD, EUR, GBP]
        isFree:
          type: boolean
        subscriptionPlans:
          type: array
          items:
            $ref: '#/components/schemas/SubscriptionPlan'
        instancePricing:
          type: array
          items:
            $ref: '#/components/schemas/InstancePricingTier'
        volumeDiscounts:
          type: array
          items:
            $ref: '#/components/schemas/VolumeDiscount'

    SubscriptionPlan:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        duration:
          type: string
          enum: [monthly, quarterly, annual]
        price:
          type: number
          format: float
        originalPrice:
          type: number
          format: float
        discountPercentage:
          type: number
          format: float
        maxInstances:
          type: integer
        features:
          type: array
          items:
            type: string
        popular:
          type: boolean

    InstancePricingTier:
      type: object
      properties:
        minInstances:
          type: integer
        maxInstances:
          type: integer
        pricePerInstance:
          type: number
          format: float
        totalPrice:
          type: number
          format: float
        discountPercentage:
          type: number
          format: float

    VolumeDiscount:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        conditions:
          type: object
          properties:
            minQuantity:
              type: integer
            minInstances:
              type: integer
            duration:
              type: string
              enum: [monthly, quarterly, annual]
            bundleItems:
              type: array
              items:
                type: string
                format: uuid
        discountType:
          type: string
          enum: [percentage, fixed_amount, buy_x_get_y]
        discountValue:
          type: number
          format: float
        maxDiscount:
          type: number
          format: float
        validFrom:
          type: string
          format: date-time
        validUntil:
          type: string
          format: date-time

    PricingResponse:
      type: object
      properties:
        itemId:
          type: string
          format: uuid
        requestedInstances:
          type: integer
        requestedDuration:
          type: string
          enum: [monthly, quarterly, annual]
        options:
          type: array
          items:
            $ref: '#/components/schemas/PricingOption'
        appliedDiscounts:
          type: array
          items:
            $ref: '#/components/schemas/AppliedDiscount'

    PricingOption:
      type: object
      properties:
        planId:
          type: string
          format: uuid
        name:
          type: string
        instances:
          type: integer
        duration:
          type: string
          enum: [monthly, quarterly, annual]
        originalPrice:
          type: number
          format: float
        finalPrice:
          type: number
          format: float
        savings:
          type: number
          format: float
        savingsPercentage:
          type: number
          format: float
        priceBreakdown:
          type: object
          properties:
            basePrice:
              type: number
              format: float
            instanceCost:
              type: number
              format: float
            discounts:
              type: number
              format: float
            taxes:
              type: number
              format: float

    AppliedDiscount:
      type: object
      properties:
        name:
          type: string
        type:
          type: string
          enum: [volume, duration, bundle, coupon]
        amount:
          type: number
          format: float
        percentage:
          type: number
          format: float

    # Cart Schemas
    Cart:
      type: object
      properties:
        id:
          type: string
          format: uuid
        items:
          type: array
          items:
            $ref: '#/components/schemas/CartItem'
        subtotal:
          type: number
          format: float
        discounts:
          type: number
          format: float
        taxes:
          type: number
          format: float
        total:
          type: number
          format: float
        currency:
          type: string
          enum: [USD, EUR, GBP]
        appliedCoupons:
          type: array
          items:
            $ref: '#/components/schemas/AppliedCoupon'
        estimatedSavings:
          type: number
          format: float

    CartItem:
      type: object
      properties:
        id:
          type: string
          format: uuid
        itemId:
          type: string
          format: uuid
        itemName:
          type: string
        planId:
          type: string
          format: uuid
        planName:
          type: string
        instances:
          type: integer
        duration:
          type: string
          enum: [monthly, quarterly, annual]
        unitPrice:
          type: number
          format: float
        totalPrice:
          type: number
          format: float
        discounts:
          type: array
          items:
            $ref: '#/components/schemas/AppliedDiscount'
        addedAt:
          type: string
          format: date-time

    AddToCartRequest:
      type: object
      required:
        - itemId
        - planId
      properties:
        itemId:
          type: string
          format: uuid
        planId:
          type: string
          format: uuid
        instances:
          type: integer
          minimum: 1
          default: 1
        duration:
          type: string
          enum: [monthly, quarterly, annual]
          default: monthly

    UpdateCartItemRequest:
      type: object
      properties:
        instances:
          type: integer
          minimum: 1
        duration:
          type: string
          enum: [monthly, quarterly, annual]

    AppliedCoupon:
      type: object
      properties:
        code:
          type: string
        name:
          type: string
        discountAmount:
          type: number
          format: float
        discountType:
          type: string
          enum: [percentage, fixed_amount]

    # Feature Flags & Access Control
    FeatureAccessCheck:
      type: object
      properties:
        featureKey:
          type: string
        hasAccess:
          type: boolean
        reason:
          type: string
          enum: [granted, no_subscription, expired, instance_limit_exceeded, feature_disabled]
        maxInstances:
          type: integer
        availableInstances:
          type: integer
        nextAvailableAt:
          type: string
          format: date-time
        upgradeOptions:
          type: array
          items:
            $ref: '#/components/schemas/UpgradeOption'

    UpgradeOption:
      type: object
      properties:
        planId:
          type: string
          format: uuid
        planName:
          type: string
        additionalInstances:
          type: integer
        price:
          type: number
          format: float
        duration:
          type: string

    # Utility Schemas
    Pagination:
      type: object
      properties:
        page:
          type: integer
        limit:
          type: integer
        total:
          type: integer
        totalPages:
          type: integer
        hasNext:
          type: boolean
        hasPrev:
          type: boolean

    StoreFilters:
      type: object
      properties:
        categories:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              count:
                type: integer
        priceRanges:
          type: array
          items:
            type: object
            properties:
              min:
                type: number
              max:
                type: number
              count:
                type: integer
        tags:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              count:
                type: integer

    Error:
      type: object
      properties:
        error:
          type: string
        message:
          type: string
        code:
          type: string
        details:
          type: object

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

tags:
  - name: Authentication
    description: User authentication and account management
  - name: Two-Factor Authentication
    description: 2FA setup and management
  - name: User Profile
    description: User profile management
  - name: Session Management
    description: User session management and security
  - name: Store
    description: Browse and view store items
  - name: Cart
    description: Shopping cart management
  - name: Features
    description: Feature flags and instance management