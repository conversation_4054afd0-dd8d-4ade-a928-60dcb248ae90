# Docker Compose configuration for production

version: '3.8'

services:
  # Production API configuration
  api:
    build:
      target: production
    restart: always
    environment:
      NODE_ENV: production
      LOG_LEVEL: info
      DEBUG_SQL: "false"
    volumes:
      - api_uploads:/app/uploads
      - api_logs:/app/logs
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Production PostgreSQL with optimizations
  postgres:
    command: >
      postgres
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G

  # Production Redis with optimizations
  redis:
    command: redis-server /usr/local/etc/redis/redis.conf --maxmemory 512mb --maxmemory-policy allkeys-lru
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Enable Nginx for production
  nginx:
    profiles: []  # Remove profile to enable by default
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M

  # Log aggregation
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: rsglider-fluentd
    restart: unless-stopped
    volumes:
      - ./docker/fluentd/fluent.conf:/fluentd/etc/fluent.conf
      - api_logs:/var/log/api
      - nginx_logs:/var/log/nginx
    networks:
      - rsglider-network
    depends_on:
      - api
      - nginx

volumes:
  api_logs:
    driver: local
  postgres_backup:
    driver: local
