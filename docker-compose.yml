version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:17-alpine
    container_name: rsglider-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: rsglider
      POSTGRES_USER: rsglider
      POSTGRES_PASSWORD: rsglider_dev_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - rsglider-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U rsglider -d rsglider"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Sessions
  redis:
    image: redis:7-alpine
    container_name: rsglider-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass rsglider_redis_password
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - rsglider-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "rsglider_redis_password", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Development tools
  adminer:
    image: adminer:4.8.1
    container_name: rsglider-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    networks:
      - rsglider-network
    environment:
      ADMINER_DEFAULT_SERVER: postgres
      ADMINER_DESIGN: pepa-linha

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: rsglider-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379:0:rsglider_redis_password
    ports:
      - "8081:8081"
    networks:
      - rsglider-network
    depends_on:
      - redis

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  rsglider-network:
    driver: bridge
