version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:17-alpine
    container_name: rsglider-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: rsglider
      POSTGRES_USER: rsglider
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-rsglider_dev_password}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - rsglider-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U rsglider -d rsglider"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Sessions
  redis:
    image: redis:7-alpine
    container_name: rsglider-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-rsglider_redis_password}
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - rsglider-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # RSGlider API Application
  api:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: rsglider-api
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3000
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: rsglider
      DATABASE_USER: rsglider
      DATABASE_PASSWORD: ${POSTGRES_PASSWORD:-rsglider_dev_password}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-rsglider_redis_password}
      JWT_SECRET: ${JWT_SECRET:-dev_jwt_secret_change_in_production}
      JWT_EXPIRES_IN: 15m
      REFRESH_TOKEN_EXPIRES_IN: 7d
      BTCPAY_SERVER_URL: ${BTCPAY_SERVER_URL:-https://btcpay.rsglider.com}
      BTCPAY_STORE_ID: ${BTCPAY_STORE_ID}
      BTCPAY_API_KEY: ${BTCPAY_API_KEY}
      BTCPAY_WEBHOOK_SECRET: ${BTCPAY_WEBHOOK_SECRET}
      GITEA_BASE_URL: ${GITEA_BASE_URL:-https://git.rsglider.com}
      GITEA_ADMIN_TOKEN: ${GITEA_ADMIN_TOKEN}
      GITEA_OIDC_CLIENT_ID: ${GITEA_OIDC_CLIENT_ID}
      GITEA_OIDC_CLIENT_SECRET: ${GITEA_OIDC_CLIENT_SECRET}
    volumes:
      - .:/app
      - /app/node_modules
      - api_uploads:/app/uploads
    ports:
      - "3000:3000"
      - "9229:9229" # Debug port
    networks:
      - rsglider-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: npm run start:dev

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: rsglider-nginx
    restart: unless-stopped
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - nginx_logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - rsglider-network
    depends_on:
      - api
    profiles:
      - production

  # BTCPay Server (Optional - for local development)
  btcpay:
    image: btcpayserver/btcpayserver:1.13.1
    container_name: rsglider-btcpay
    restart: unless-stopped
    environment:
      BTCPAY_NETWORK: regtest
      BTCPAY_BIND: 0.0.0.0:49392
      BTCPAY_EXTERNALURL: http://localhost:49392
    volumes:
      - btcpay_data:/datadir
    ports:
      - "49392:49392"
    networks:
      - rsglider-network
    profiles:
      - btcpay

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  api_uploads:
    driver: local
  nginx_logs:
    driver: local
  btcpay_data:
    driver: local

networks:
  rsglider-network:
    driver: bridge
