# Docker Compose override for development
# This file is automatically loaded by docker-compose

version: '3.8'

services:
  # Development overrides for API
  api:
    build:
      target: development
    environment:
      NODE_ENV: development
      DEBUG_SQL: "true"
      LOG_LEVEL: debug
    volumes:
      - .:/app
      - /app/node_modules
      - api_uploads:/app/uploads
    command: npm run start:dev
    
  # Add development tools
  adminer:
    image: adminer:4.8.1
    container_name: rsglider-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    networks:
      - rsglider-network
    environment:
      ADMINER_DEFAULT_SERVER: postgres
      ADMINER_DESIGN: pepa-linha

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: rsglider-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379:0:${REDIS_PASSWORD:-rsglider_redis_password}
    ports:
      - "8081:8081"
    networks:
      - rsglider-network
    depends_on:
      - redis

  # MailHog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: rsglider-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - rsglider-network
