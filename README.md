# RSGlider API

> Comprehensive NestJS API for the RSGlider platform with developer marketplace, BTCPay Server integration, and advanced session management.

## 🚀 Features

### 🔐 **Authentication & Security**
- JWT-based authentication with refresh token rotation
- Two-factor authentication (TOTP) with backup codes
- Role-based access control (RBAC)
- Comprehensive session management across platforms

### 🧑‍💻 **Developer Marketplace**
- Admin-controlled Gitea integration with SSO
- Repository-to-marketplace publishing
- Real-time analytics and revenue tracking
- Flexible BTCPay Server payouts (Bitcoin-only)

### 💳 **Payment System**
- Direct Bitcoin payments via BTCPay Server
- No stored funds (security-first approach)
- Real-time payment tracking with webhooks
- Automated revenue sharing and payouts

### 📱 **Multi-Platform Session Control**
- Desktop app (Tauri) device registration and limits
- Web app device verification with email validation
- Bot session management with subscription-based limits
- Cross-platform session synchronization

## 📁 Project Structure

```
rsglider-api/
├── api-docs/                      # API Documentation
│   ├── openapi.yaml              # Complete OpenAPI 3.0.3 specification
│   ├── README.md                 # API documentation overview
│   ├── nestjs-integration.md     # NestJS implementation guide
│   └── examples/                 # Testing resources
│       ├── postman-collection.json
│       ├── environments.json
│       └── curl-examples.md
├── docs/                         # Implementation guides
│   ├── BTCPAY_INTEGRATION.md
│   ├── GITEA_INTEGRATION_PLAN.md
│   └── SESSION_MANAGEMENT.md
├── src/                          # NestJS source code (to be created)
├── .spectral.yml                 # OpenAPI linting rules
├── .yamllint.yml                 # YAML formatting rules
├── .yamlvalidate                 # YAML validation config
└── package.json                  # Dependencies and scripts
```

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+ and npm 9+
- PostgreSQL 14+
- Redis 6+ (for sessions)
- BTCPay Server instance
- Gitea instance

### Installation
```bash
# Clone repository
git clone https://github.com/rsglider/rsglider-api.git
cd rsglider-api

# Install dependencies
npm install

# Copy environment template
cp .env.example .env

# Configure environment variables
# Edit .env with your database, BTCPay, and Gitea settings

# Run database migrations
npm run migration:run

# Start development server
npm run start:dev
```

### Environment Variables
```bash
# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=rsglider
DATABASE_USER=rsglider
DATABASE_PASSWORD=your_password

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d

# BTCPay Server
BTCPAY_SERVER_URL=https://btcpay.rsglider.com
BTCPAY_STORE_ID=your_store_id
BTCPAY_API_KEY=your_api_key
BTCPAY_WEBHOOK_SECRET=your_webhook_secret

# Gitea Integration
GITEA_BASE_URL=https://git.rsglider.com
GITEA_ADMIN_TOKEN=your_admin_token
GITEA_OIDC_CLIENT_ID=your_client_id
GITEA_OIDC_CLIENT_SECRET=your_client_secret

# Redis (Sessions)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
```

## 📚 API Documentation

### Interactive Documentation
- **Development**: http://localhost:3000/api/docs
- **Staging**: https://api-staging.rsglider.com/api/docs
- **Production**: https://api.rsglider.com/api/docs

### Testing Resources
- **Postman Collection**: `api-docs/examples/postman-collection.json`
- **cURL Examples**: `api-docs/examples/curl-examples.md`
- **Environment Files**: `api-docs/examples/environments.json`

## 🧪 Testing & Validation

### API Validation
```bash
# Validate OpenAPI specification
npm run validate:api

# Lint YAML files
npm run validate:yaml

# Check OpenAPI compliance
npm run validate:openapi

# Run Postman tests
npm run postman:test
```

### Unit & Integration Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:cov

# Run end-to-end tests
npm run test:e2e
```

## 🚀 Deployment

### Docker Deployment
```bash
# Build Docker image
docker build -t rsglider-api .

# Run with Docker Compose
docker-compose up -d
```

### Production Deployment
```bash
# Build for production
npm run build

# Start production server
npm run start:prod
```

## 📖 Key Endpoints

### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - Login with 2FA support
- `POST /auth/refresh` - Token refresh

### User Management
- `GET /users/me` - Current user profile
- `GET /users/me/sessions` - Session management
- `POST /users/me/2fa/setup` - Two-factor authentication

### Developer Integration
- `GET /developers/gitea/profile` - Gitea integration status
- `POST /developers/repositories/{id}/publish` - Publish to marketplace
- `GET /developers/analytics/overview` - Analytics dashboard

### Marketplace
- `GET /store/items` - Browse marketplace
- `POST /cart/checkout` - BTCPay Server checkout
- `GET /orders/{id}/status` - Payment status

### Admin Controls
- `POST /admin/developers/{id}/promote` - Promote to developer
- `PUT /admin/payout-settings` - Configure payouts
- `GET /admin/users` - User management

## 🔧 Configuration

### Payout Settings
- **Default frequency**: Weekly
- **Minimum payout**: $50 USD (Bitcoin equivalent)
- **Holding period**: 7 days
- **Revenue share**: 70% developer, 30% platform

### Session Limits (by subscription)
- **Free**: 1 desktop, 1 bot, 3 web sessions
- **Pro**: 2 desktops, 5 bots, 10 web sessions
- **Enterprise**: 10 desktops, 25 bots, 50 web sessions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and validation
5. Submit a pull request

### Code Quality
- Follow NestJS conventions
- Add Swagger decorators to all DTOs
- Include unit tests for new features
- Validate OpenAPI spec changes

## 📄 License

This project is proprietary and confidential. All rights reserved.

## 🆘 Support

- **Documentation**: https://docs.rsglider.com
- **Issues**: https://github.com/rsglider/rsglider-api/issues
- **Discord**: https://discord.gg/rsglider

---

Built with ❤️ by the RSGlider team
